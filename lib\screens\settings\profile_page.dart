import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_widget.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _departmentController = TextEditingController();
  
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.userModel;
    
    if (user != null) {
      _nameController.text = user.name;
      _phoneController.text = user.phone ?? '';
      _departmentController.text = user.department ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = context.read<AuthProvider>();
    final currentUser = authProvider.userModel;
    
    if (currentUser == null) return;

    final updatedUser = currentUser.copyWith(
      name: _nameController.text.trim(),
      phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
      department: _departmentController.text.trim().isNotEmpty ? _departmentController.text.trim() : null,
    );

    final success = await authProvider.updateUserData(updatedUser);
    
    if (success && mounted) {
      setState(() {
        _isEditing = false;
      });
      _showSuccessSnackBar('Profile updated successfully');
    } else if (mounted) {
      _showErrorSnackBar(authProvider.errorMessage ?? 'Failed to update profile');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final authProvider = context.watch<AuthProvider>();
    final settingsProvider = context.watch<SettingsProvider>();
    final isArabic = settingsProvider.isArabic;
    final user = authProvider.userModel;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'الملف الشخصي' : 'Profile'),
        centerTitle: true,
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(Icons.edit),
              tooltip: isArabic ? 'تعديل' : 'Edit',
            ),
        ],
      ),
      body: authProvider.isLoading
          ? const Center(child: LoadingWidget())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Profile Picture Section
                    _buildProfilePictureSection(context, user, isArabic),
                    
                    const SizedBox(height: 32),
                    
                    // Profile Information
                    _buildProfileForm(context, user, isArabic),
                    
                    const SizedBox(height: 32),
                    
                    // Action Buttons
                    if (_isEditing) _buildEditingButtons(context, authProvider, isArabic),
                    
                    const SizedBox(height: 24),
                    
                    // Additional Options
                    _buildAdditionalOptions(context, isArabic),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfilePictureSection(BuildContext context, user, bool isArabic) {
    return Column(
      children: [
        Stack(
          children: [
            CircleAvatar(
              radius: 60,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: user?.profileImageUrl != null
                  ? ClipOval(
                      child: Image.network(
                        user!.profileImageUrl!,
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Text(
                            user.initials,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                      ),
                    )
                  : Text(
                      user?.initials ?? 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
            if (_isEditing)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () {
                      _showImagePickerDialog(context, isArabic);
                    },
                    icon: const Icon(Icons.camera_alt, color: Colors.white),
                    iconSize: 20,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          user?.name ?? 'User',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: user?.isAdmin == true 
                ? Colors.red.withOpacity(0.1)
                : Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            user?.isAdmin == true 
                ? (isArabic ? 'مدير' : 'Admin')
                : (isArabic ? 'موظف' : 'Employee'),
            style: TextStyle(
              color: user?.isAdmin == true ? Colors.red : Colors.blue,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileForm(BuildContext context, user, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Email (Read-only)
        CustomTextField(
          labelText: isArabic ? 'البريد الإلكتروني' : 'Email',
          controller: TextEditingController(text: user?.email ?? ''),
          enabled: false,
          prefixIcon: Icons.email_outlined,
        ),
        
        const SizedBox(height: 16),
        
        // Name
        CustomTextField(
          controller: _nameController,
          labelText: isArabic ? 'الاسم الكامل' : 'Full Name',
          enabled: _isEditing,
          prefixIcon: Icons.person_outlined,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return isArabic ? 'الاسم مطلوب' : 'Name is required';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // Phone
        CustomTextField(
          controller: _phoneController,
          labelText: isArabic ? 'رقم الهاتف' : 'Phone Number',
          enabled: _isEditing,
          keyboardType: TextInputType.phone,
          prefixIcon: Icons.phone_outlined,
        ),
        
        const SizedBox(height: 16),
        
        // Department
        CustomTextField(
          controller: _departmentController,
          labelText: isArabic ? 'القسم' : 'Department',
          enabled: _isEditing,
          prefixIcon: Icons.business_outlined,
        ),
        
        const SizedBox(height: 16),
        
        // Member Since (Read-only)
        CustomTextField(
          labelText: isArabic ? 'عضو منذ' : 'Member Since',
          controller: TextEditingController(
            text: user?.createdAt != null 
                ? '${user!.createdAt.day}/${user.createdAt.month}/${user.createdAt.year}'
                : '',
          ),
          enabled: false,
          prefixIcon: Icons.calendar_today_outlined,
        ),
      ],
    );
  }

  Widget _buildEditingButtons(BuildContext context, AuthProvider authProvider, bool isArabic) {
    return Row(
      children: [
        Expanded(
          child: OutlineButton(
            text: isArabic ? 'إلغاء' : 'Cancel',
            onPressed: () {
              setState(() {
                _isEditing = false;
                _loadUserData(); // Reset form
              });
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: authProvider.isLoading
              ? const LoadingWidget()
              : PrimaryButton(
                  text: isArabic ? 'حفظ' : 'Save',
                  onPressed: _saveProfile,
                ),
        ),
      ],
    );
  }

  Widget _buildAdditionalOptions(BuildContext context, bool isArabic) {
    return Column(
      children: [
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.lock_outline),
                title: Text(isArabic ? 'تغيير كلمة المرور' : 'Change Password'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  context.push('/settings/change-password');
                },
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.settings),
                title: Text(isArabic ? 'الإعدادات' : 'Settings'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  context.push('/settings');
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showImagePickerDialog(BuildContext context, bool isArabic) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'تغيير الصورة الشخصية' : 'Change Profile Picture'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: Text(isArabic ? 'التقاط صورة' : 'Take Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _showSuccessSnackBar('Camera feature - Coming Soon');
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(isArabic ? 'اختيار من المعرض' : 'Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _showSuccessSnackBar('Gallery feature - Coming Soon');
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(isArabic ? 'إلغاء' : 'Cancel'),
            ),
          ],
        );
      },
    );
  }
}
