import 'package:intl/intl.dart';
import 'constants.dart';

class DateHelpers {
  // Format date for display
  static String formatDate(DateTime date, {String? format}) {
    final formatter = DateFormat(format ?? AppConstants.displayDateFormat);
    return formatter.format(date);
  }
  
  // Format time for display
  static String formatTime(DateTime date) {
    final formatter = DateFormat(AppConstants.timeFormat);
    return formatter.format(date);
  }
  
  // Format date and time for display
  static String formatDateTime(DateTime date) {
    final formatter = DateFormat(AppConstants.dateTimeFormat);
    return formatter.format(date);
  }
  
  // Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }
  
  // Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }
  
  // Get start of week (Monday)
  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }
  
  // Get end of week (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }
  
  // Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }
  
  // Get end of month
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }
  
  // Get start of year
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }
  
  // Get end of year
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }
  
  // Check if two dates are on the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
  
  // Check if date is today
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }
  
  // Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }
  
  // Check if date is this week
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final weekStart = startOfWeek(now);
    final weekEnd = endOfWeek(now);
    return date.isAfter(weekStart.subtract(const Duration(seconds: 1))) &&
           date.isBefore(weekEnd.add(const Duration(seconds: 1)));
  }
  
  // Check if date is this month
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }
  
  // Check if date is this year
  static bool isThisYear(DateTime date) {
    return date.year == DateTime.now().year;
  }
  
  // Get relative date string (Today, Yesterday, etc.)
  static String getRelativeDateString(DateTime date) {
    if (isToday(date)) {
      return 'today';
    } else if (isYesterday(date)) {
      return 'yesterday';
    } else if (isThisWeek(date)) {
      return 'this_week';
    } else if (isThisMonth(date)) {
      return 'this_month';
    } else if (isThisYear(date)) {
      return 'this_year';
    } else {
      return formatDate(date);
    }
  }
  
  // Get days between two dates
  static int daysBetween(DateTime from, DateTime to) {
    from = startOfDay(from);
    to = startOfDay(to);
    return to.difference(from).inDays;
  }
  
  // Get age from birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }
  
  // Get list of dates in range
  static List<DateTime> getDatesInRange(DateTime start, DateTime end) {
    final dates = <DateTime>[];
    DateTime current = startOfDay(start);
    final endDate = startOfDay(end);
    
    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }
    
    return dates;
  }
  
  // Get month name
  static String getMonthName(int month, {bool isArabic = false}) {
    if (isArabic) {
      const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      return arabicMonths[month - 1];
    } else {
      const englishMonths = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return englishMonths[month - 1];
    }
  }
  
  // Get day name
  static String getDayName(int weekday, {bool isArabic = false}) {
    if (isArabic) {
      const arabicDays = [
        'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
      ];
      return arabicDays[weekday - 1];
    } else {
      const englishDays = [
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
      ];
      return englishDays[weekday - 1];
    }
  }
  
  // Parse date string
  static DateTime? parseDate(String dateString, {String? format}) {
    try {
      final formatter = DateFormat(format ?? AppConstants.dateFormat);
      return formatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }
  
  // Get quarter of year
  static int getQuarter(DateTime date) {
    return ((date.month - 1) ~/ 3) + 1;
  }
  
  // Get first day of quarter
  static DateTime startOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final firstMonthOfQuarter = (quarter - 1) * 3 + 1;
    return DateTime(date.year, firstMonthOfQuarter, 1);
  }
  
  // Get last day of quarter
  static DateTime endOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final lastMonthOfQuarter = quarter * 3;
    return endOfMonth(DateTime(date.year, lastMonthOfQuarter, 1));
  }
}
