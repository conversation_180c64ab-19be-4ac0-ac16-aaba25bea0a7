import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../services/currency_service.dart';

/// واجهة اختيار العملة
class CurrencyPicker extends StatelessWidget {
  final Currency selectedCurrency;
  final Function(Currency) onCurrencySelected;

  const CurrencyPicker({
    super.key,
    required this.selectedCurrency,
    required this.onCurrencySelected,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return AlertDialog(
      title: Text(localizations.selectCurrency),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: CurrencyService.supportedCurrencies.length,
          itemBuilder: (context, index) {
            final currency = CurrencyService.supportedCurrencies[index];
            final isSelected = currency == selectedCurrency;

            return ListTile(
              leading: Text(
                currency.flag,
                style: const TextStyle(fontSize: 24),
              ),
              title: Text(
                CurrencyService.getCurrencyName(currency, isArabic),
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              subtitle: Text(
                '${currency.code} - ${CurrencyService.getCurrencySymbol(currency, isArabic)}',
                style: TextStyle(
                  color: isSelected ? Theme.of(context).primaryColor : null,
                ),
              ),
              trailing:
                  isSelected
                      ? Icon(
                        Icons.check_circle,
                        color: Theme.of(context).primaryColor,
                      )
                      : null,
              onTap: () {
                onCurrencySelected(currency);
                Navigator.of(context).pop();
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(localizations.cancel),
        ),
      ],
    );
  }
}

/// زر اختيار العملة
class CurrencySelectionButton extends StatelessWidget {
  final Currency selectedCurrency;
  final Function(Currency) onCurrencyChanged;

  const CurrencySelectionButton({
    super.key,
    required this.selectedCurrency,
    required this.onCurrencyChanged,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return PopupMenuButton<Currency>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(selectedCurrency.flag, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 4),
          Text(
            CurrencyService.getCurrencySymbol(selectedCurrency, isArabic),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const Icon(Icons.arrow_drop_down, size: 16),
        ],
      ),
      tooltip: localizations.currencySelection,
      onSelected: onCurrencyChanged,
      itemBuilder: (BuildContext context) {
        return CurrencyService.supportedCurrencies.map((Currency currency) {
          return PopupMenuItem<Currency>(
            value: currency,
            child: Row(
              children: [
                Text(currency.flag, style: const TextStyle(fontSize: 20)),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        CurrencyService.getCurrencyName(currency, isArabic),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Text(
                        '${currency.code} - ${CurrencyService.getCurrencySymbol(currency, isArabic)}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                if (currency == selectedCurrency)
                  Icon(
                    Icons.check,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
              ],
            ),
          );
        }).toList();
      },
    );
  }
}

/// بطاقة عرض العملة الحالية
class CurrentCurrencyCard extends StatelessWidget {
  final Currency currency;
  final VoidCallback onTap;

  const CurrentCurrencyCard({
    super.key,
    required this.currency,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.transparent,
          child: Text(currency.flag, style: const TextStyle(fontSize: 24)),
        ),
        title: Text(
          CurrencyService.getCurrencyName(currency, isArabic),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${currency.code} - ${CurrencyService.getCurrencySymbol(currency, isArabic)}',
        ),
        trailing: const Icon(Icons.edit),
        onTap: onTap,
      ),
    );
  }
}

// Bottom Sheet for Currency Selection
class CurrencySelectionBottomSheet extends StatelessWidget {
  final Currency selectedCurrency;
  final Function(Currency) onCurrencySelected;

  const CurrencySelectionBottomSheet({
    super.key,
    required this.selectedCurrency,
    required this.onCurrencySelected,
  });

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.onSurfaceVariant.withOpacity(0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Title
          Text(
            isArabic ? 'اختيار العملة' : 'Select Currency',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // Currency List
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: CurrencyService.supportedCurrencies.length,
              itemBuilder: (context, index) {
                final currency = CurrencyService.supportedCurrencies[index];
                final isSelected = currency.code == selectedCurrency.code;

                return ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Theme.of(
                                context,
                              ).colorScheme.primary.withOpacity(0.1)
                              : Theme.of(
                                context,
                              ).colorScheme.surfaceVariant.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        CurrencyService.getCurrencySymbol(currency, isArabic),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color:
                              isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    CurrencyService.getCurrencyName(currency, isArabic),
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  subtitle: Text(
                    currency.code,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  trailing:
                      isSelected
                          ? Icon(
                            Icons.check_circle,
                            color: Theme.of(context).colorScheme.primary,
                          )
                          : null,
                  selected: isSelected,
                  selectedTileColor: Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withOpacity(0.3),
                  onTap: () {
                    onCurrencySelected(currency);
                  },
                );
              },
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
