import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/settings_provider.dart';
import '../../utils/admin_setup.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_widget.dart';

class AdminSetupPage extends StatefulWidget {
  const AdminSetupPage({super.key});

  @override
  State<AdminSetupPage> createState() => _AdminSetupPageState();
}

class _AdminSetupPageState extends State<AdminSetupPage> {
  bool _isLoading = false;
  bool _adminCreated = false;
  String _statusMessage = '';

  Future<void> _createAdminAccount() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إنشاء حساب المدير...';
    });

    try {
      final success = await AdminSetup.createAdminAccount();
      
      setState(() {
        _isLoading = false;
        _adminCreated = success;
        _statusMessage = success 
            ? 'تم إنشاء حساب المدير بنجاح!' 
            : 'فشل في إنشاء حساب المدير';
      });

      if (success) {
        AdminSetup.printAdminInfo();
        _showSuccessDialog();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'خطأ: $e';
      });
    }
  }

  void _showSuccessDialog() {
    final isArabic = context.read<SettingsProvider>().isArabic;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 32),
              const SizedBox(width: 12),
              Text(isArabic ? 'تم بنجاح!' : 'Success!'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isArabic 
                    ? 'تم إنشاء حساب المدير بنجاح بالبيانات التالية:'
                    : 'Admin account created successfully with the following details:',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 16),
              _buildInfoRow(
                isArabic ? 'البريد الإلكتروني:' : 'Email:',
                AdminSetup.adminEmail,
              ),
              _buildInfoRow(
                isArabic ? 'كلمة المرور:' : 'Password:',
                AdminSetup.adminPassword,
              ),
              _buildInfoRow(
                isArabic ? 'الاسم:' : 'Name:',
                AdminSetup.adminName,
              ),
              _buildInfoRow(
                isArabic ? 'الدور:' : 'Role:',
                isArabic ? 'مدير' : 'Admin',
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isArabic 
                            ? 'يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول'
                            : 'It is recommended to change the password after first login',
                        style: TextStyle(color: Colors.orange.shade700),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop(); // العودة للصفحة السابقة
              },
              child: Text(isArabic ? 'موافق' : 'OK'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final settingsProvider = context.watch<SettingsProvider>();
    final isArabic = settingsProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'إعداد حساب المدير' : 'Admin Account Setup'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.admin_panel_settings,
                      size: 64,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      isArabic 
                          ? 'إعداد حساب المدير الرئيسي'
                          : 'Setup Main Admin Account',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isArabic 
                          ? 'سيتم إنشاء حساب المدير بالبيانات المحددة مسبقاً'
                          : 'Admin account will be created with predefined credentials',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Admin Details Preview
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isArabic ? 'بيانات حساب المدير:' : 'Admin Account Details:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      isArabic ? 'البريد الإلكتروني:' : 'Email:',
                      AdminSetup.adminEmail,
                    ),
                    _buildInfoRow(
                      isArabic ? 'كلمة المرور:' : 'Password:',
                      '••••••••',
                    ),
                    _buildInfoRow(
                      isArabic ? 'الاسم:' : 'Name:',
                      AdminSetup.adminName,
                    ),
                    _buildInfoRow(
                      isArabic ? 'القسم:' : 'Department:',
                      AdminSetup.adminDepartment,
                    ),
                    _buildInfoRow(
                      isArabic ? 'الدور:' : 'Role:',
                      isArabic ? 'مدير' : 'Admin',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Status Message
            if (_statusMessage.isNotEmpty)
              Card(
                color: _adminCreated 
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        _adminCreated ? Icons.check_circle : Icons.info,
                        color: _adminCreated ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _statusMessage,
                          style: TextStyle(
                            color: _adminCreated ? Colors.green : Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Create Button
            if (_isLoading)
              const LoadingWidget()
            else if (!_adminCreated)
              CustomButton(
                text: isArabic ? 'إنشاء حساب المدير' : 'Create Admin Account',
                onPressed: _createAdminAccount,
                isFullWidth: true,
                icon: Icons.admin_panel_settings,
              )
            else
              CustomButton(
                text: isArabic ? 'تم الإنشاء بنجاح' : 'Successfully Created',
                onPressed: null,
                isFullWidth: true,
                icon: Icons.check_circle,
                backgroundColor: Colors.green,
              ),

            const SizedBox(height: 16),

            // Instructions
            Text(
              isArabic 
                  ? 'ملاحظة: سيتم إنشاء الحساب في Firebase Authentication و Firestore Database'
                  : 'Note: Account will be created in Firebase Authentication and Firestore Database',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
