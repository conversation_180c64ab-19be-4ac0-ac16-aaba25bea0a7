# 🧪 دليل اختبار حساب المدير - تطبيق متتبع المبيعات

## 📋 معلومات حساب المدير

**بيانات الحساب:**
- 📧 البريد الإلكتروني: `<EMAIL>`
- 🔑 كلمة المرور: `M1158m`
- 👤 الاسم: `محمد جاسم`
- 🏢 القسم: `الإدارة`
- 🎯 الدور: `admin` (مدير)

## 🚀 خطوات الاختبار

### الخطوة 1: إعداد Firebase (مطلوب أولاً)

1. **إنشاء مشروع Firebase:**
   - اذهب إلى [Firebase Console](https://console.firebase.google.com/)
   - أنشئ مشروع جديد باسم `sales-tracker-khalaifat`

2. **إضافة تطبيق الويب:**
   - اضغط على أيقونة الويب `</>`
   - سجل التطبيق واحصل على إعدادات Firebase

3. **تحديث إعدادات التطبيق:**
   - افتح `lib/firebase_options.dart`
   - استبدل القيم في `FirebaseOptions web` بالقيم من Firebase Console

4. **تفعيل الخدمات:**
   - **Authentication**: فعّل Email/Password
   - **Firestore Database**: أنشئ قاعدة بيانات في Test mode

### الخطوة 2: تشغيل التطبيق

```bash
flutter run -d chrome --web-port 8084
```

### الخطوة 3: إنشاء حساب المدير

#### الطريقة الأولى: تلقائياً في أول تشغيل
1. عند تشغيل التطبيق لأول مرة، سيتم إنشاء حساب المدير تلقائياً
2. تحقق من وحدة التحكم (Console) لرؤية رسالة نجاح الإنشاء

#### الطريقة الثانية: من خلال صفحة الإعدادات
1. سجل دخول كمدير (إذا كان لديك حساب مدير آخر)
2. اذهب إلى الإعدادات > إعدادات المدير > إعداد حساب مدير جديد
3. اضغط "إنشاء حساب المدير"

#### الطريقة الثالثة: يدوياً من التطبيق
1. اذهب إلى صفحة "إنشاء حساب"
2. أدخل البيانات:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `M1158m`
   - الاسم: `محمد جاسم`
   - القسم: `الإدارة`
3. بعد إنشاء الحساب، اذهب إلى Firebase Console
4. في Firestore Database، ابحث عن المستخدم وعدّل:
   ```
   role: "admin"
   ```

### الخطوة 4: اختبار تسجيل الدخول

1. **اذهب إلى صفحة تسجيل الدخول**
2. **أدخل البيانات:**
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `M1158m`
3. **اضغط "تسجيل الدخول"**

### الخطوة 5: التحقق من صلاحيات المدير

#### أ. التوجيه التلقائي
- ✅ يجب أن يتم توجيهك إلى لوحة تحكم المدير (`/admin`)
- ❌ إذا تم توجيهك إلى الصفحة الرئيسية العادية، فهناك مشكلة في الصلاحيات

#### ب. لوحة تحكم المدير
تحقق من وجود:
- 📊 إحصائيات شاملة لجميع المبيعات
- 👥 عدد الموظفين النشطين
- 📈 متوسط المعاملات
- 🎯 أفضل الفئات

#### ج. القائمة الجانبية
تحقق من وجود خيارات المدير:
- 🏠 لوحة التحكم
- 🛒 جميع المبيعات
- 👥 إدارة المستخدمين
- 📊 التقارير
- ➕ إضافة موظف

#### د. الإجراءات السريعة
تحقق من إمكانية الوصول إلى:
- 📋 جميع المبيعات
- 👥 إدارة المستخدمين
- 📊 التقارير
- ➕ إضافة موظف

### الخطوة 6: اختبار الوظائف الإدارية

#### أ. عرض جميع المبيعات
1. اضغط على "جميع المبيعات"
2. تحقق من إمكانية رؤية مبيعات جميع الموظفين

#### ب. إدارة المستخدمين
1. اضغط على "إدارة المستخدمين"
2. تحقق من قائمة المستخدمين
3. جرب إضافة موظف جديد

#### ج. التقارير
1. اضغط على "التقارير"
2. تحقق من الإحصائيات المفصلة

### الخطوة 7: اختبار الأمان

#### أ. حماية المسارات
- جرب الوصول إلى `/admin` بدون تسجيل دخول
- يجب أن يتم توجيهك إلى صفحة تسجيل الدخول

#### ب. صلاحيات قاعدة البيانات
- تحقق من إمكانية قراءة وكتابة جميع البيانات
- تحقق من عدم إمكانية الموظفين العاديين الوصول لبيانات المدير

## 🔍 استكشاف الأخطاء

### مشكلة: لا يمكن تسجيل الدخول
**الحلول:**
1. تأكد من تفعيل Email/Password في Firebase Authentication
2. تحقق من صحة البريد الإلكتروني وكلمة المرور
3. تحقق من إعدادات Firebase في `firebase_options.dart`

### مشكلة: يتم توجيهي للصفحة العادية بدلاً من لوحة المدير
**الحلول:**
1. تحقق من حقل `role` في Firestore (يجب أن يكون `"admin"`)
2. تأكد من أن `isAdmin` getter في UserModel يعمل بشكل صحيح
3. تحقق من منطق التوجيه في AppRouter

### مشكلة: لا يمكن الوصول للبيانات
**الحلول:**
1. تحقق من قواعد Firestore Security Rules
2. تأكد من أن المستخدم مصادق عليه
3. تحقق من صلاحيات المدير في قاعدة البيانات

### مشكلة: خطأ في Firebase
**الحلول:**
1. تأكد من إعداد مشروع Firebase بشكل صحيح
2. تحقق من إعدادات `firebase_options.dart`
3. تأكد من تفعيل الخدمات المطلوبة في Firebase Console

## ✅ قائمة التحقق النهائية

- [ ] تم إعداد مشروع Firebase
- [ ] تم تحديث إعدادات Firebase في التطبيق
- [ ] تم تفعيل Authentication و Firestore
- [ ] تم إنشاء حساب المدير بنجاح
- [ ] يمكن تسجيل الدخول بحساب المدير
- [ ] يتم التوجيه إلى لوحة تحكم المدير
- [ ] تظهر جميع خيارات المدير في القائمة
- [ ] يمكن الوصول لجميع المبيعات
- [ ] يمكن إدارة المستخدمين
- [ ] تعمل التقارير والإحصائيات
- [ ] الأمان يعمل بشكل صحيح

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم (Console) للأخطاء
2. راجع Firebase Console للتأكد من الإعدادات
3. تأكد من اتباع جميع الخطوات بالترتيب

**حساب المدير جاهز للاستخدام! 🎉**
