import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/sales_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/navigation/app_drawer.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final salesProvider = context.read<SalesProvider>();
    // Load all sales for admin dashboard
    await salesProvider.loadSales();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final settingsProvider = context.watch<SettingsProvider>();
    final authProvider = context.watch<AuthProvider>();
    final salesProvider = context.watch<SalesProvider>();
    final isArabic = settingsProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'لوحة تحكم المدير' : 'Admin Dashboard'),
        centerTitle: true,
        actions: [
          // Notifications
          IconButton(
            onPressed: () {
              _showNotifications(context);
            },
            icon: const Icon(Icons.notifications_outlined),
            tooltip: isArabic ? 'الإشعارات' : 'Notifications',
          ),
          // Settings
          IconButton(
            onPressed: () {
              context.push(AppConstants.settingsRoute);
            },
            icon: const Icon(Icons.settings_outlined),
            tooltip: isArabic ? 'الإعدادات' : 'Settings',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: salesProvider.isLoading
            ? const Center(child: LoadingWidget())
            : SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Section
                    _buildWelcomeSection(context, authProvider, isArabic),
                    
                    const SizedBox(height: 24),
                    
                    // Overview Statistics
                    _buildOverviewSection(context, salesProvider, settingsProvider, isArabic),
                    
                    const SizedBox(height: 24),
                    
                    // Quick Actions
                    _buildQuickActionsSection(context, isArabic),
                    
                    const SizedBox(height: 24),
                    
                    // Performance Charts
                    _buildPerformanceSection(context, salesProvider, settingsProvider, isArabic),
                    
                    const SizedBox(height: 24),
                    
                    // Recent Activity
                    _buildRecentActivitySection(context, salesProvider, settingsProvider, isArabic),
                  ],
                ),
              ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          context.push('/admin/add-user');
        },
        icon: const Icon(Icons.person_add),
        label: Text(isArabic ? 'إضافة موظف' : 'Add Employee'),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, AuthProvider authProvider, bool isArabic) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.admin_panel_settings,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'مرحباً، ${authProvider.displayName}' : 'Welcome, ${authProvider.displayName}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        isArabic ? 'مدير النظام' : 'System Administrator',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              isArabic 
                  ? 'إدارة شاملة لنظام المبيعات والموظفين'
                  : 'Comprehensive management of sales system and employees',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewSection(BuildContext context, SalesProvider salesProvider,
      SettingsProvider settingsProvider, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'نظرة عامة' : 'Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildOverviewCard(
              context,
              title: isArabic ? 'إجمالي المبيعات' : 'Total Sales',
              value: settingsProvider.formatAmount(salesProvider.totalSales),
              icon: Icons.trending_up,
              color: Colors.green,
              subtitle: isArabic ? 'جميع الفترات' : 'All time',
            ),
            _buildOverviewCard(
              context,
              title: isArabic ? 'المعاملات' : 'Transactions',
              value: salesProvider.totalTransactions.toString(),
              icon: Icons.receipt_long,
              color: Colors.blue,
              subtitle: isArabic ? 'إجمالي' : 'Total',
            ),
            _buildOverviewCard(
              context,
              title: isArabic ? 'متوسط المعاملة' : 'Avg Transaction',
              value: settingsProvider.formatAmount(salesProvider.averageTransaction),
              icon: Icons.analytics,
              color: Colors.orange,
              subtitle: isArabic ? 'لكل معاملة' : 'Per transaction',
            ),
            _buildOverviewCard(
              context,
              title: isArabic ? 'الموظفين النشطين' : 'Active Employees',
              value: salesProvider.salesByEmployee.length.toString(),
              icon: Icons.people,
              color: Colors.purple,
              subtitle: isArabic ? 'موظف' : 'Employees',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOverviewCard(BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(Icons.arrow_upward, color: color, size: 16),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'إجراءات سريعة' : 'Quick Actions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildActionCard(
              context,
              title: isArabic ? 'جميع المبيعات' : 'All Sales',
              icon: Icons.shopping_cart,
              color: Colors.blue,
              onTap: () => context.push('/admin/all-sales'),
            ),
            _buildActionCard(
              context,
              title: isArabic ? 'إدارة المستخدمين' : 'Manage Users',
              icon: Icons.people,
              color: Colors.green,
              onTap: () => context.push('/admin/manage-users'),
            ),
            _buildActionCard(
              context,
              title: isArabic ? 'التقارير' : 'Reports',
              icon: Icons.assessment,
              color: Colors.orange,
              onTap: () => context.push('/admin/reports'),
            ),
            _buildActionCard(
              context,
              title: isArabic ? 'إضافة موظف' : 'Add Employee',
              icon: Icons.person_add,
              color: Colors.purple,
              onTap: () => context.push('/admin/add-user'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceSection(BuildContext context, SalesProvider salesProvider,
      SettingsProvider settingsProvider, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الأداء' : 'Performance',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  isArabic ? 'أفضل الفئات' : 'Top Categories',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                if (salesProvider.topCategories.isEmpty)
                  Text(
                    isArabic ? 'لا توجد بيانات' : 'No data available',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  )
                else
                  ...salesProvider.topCategories.take(3).map((entry) => 
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Expanded(child: Text(entry.key)),
                          Text(
                            settingsProvider.formatAmount(entry.value),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                  ).toList(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivitySection(BuildContext context, SalesProvider salesProvider,
      SettingsProvider settingsProvider, bool isArabic) {
    final recentSales = salesProvider.sales.take(5).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              isArabic ? 'النشاط الأخير' : 'Recent Activity',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.push('/admin/all-sales'),
              child: Text(isArabic ? 'عرض الكل' : 'View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (recentSales.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Text(
                  isArabic ? 'لا توجد مبيعات حديثة' : 'No recent sales',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          )
        else
          ...recentSales.map((sale) => Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                child: Text(sale.userName.substring(0, 1).toUpperCase()),
              ),
              title: Text(sale.productName),
              subtitle: Text(
                '${isArabic ? 'بواسطة' : 'by'} ${sale.userName}',
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    settingsProvider.formatAmount(sale.totalPrice),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  Text(
                    '${sale.date.day}/${sale.date.month}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          )).toList(),
      ],
    );
  }

  void _showNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notifications - Coming Soon')),
    );
  }
}
