import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/language_service.dart';
import '../services/currency_service.dart';
import '../utils/constants.dart';

class SettingsProvider extends ChangeNotifier {
  Locale _locale = LanguageService.defaultLocale;
  Currency _currency = CurrencyService.defaultCurrency;
  ThemeMode _themeMode = ThemeMode.system;
  bool _isLoading = false;

  // Getters
  Locale get locale => _locale;
  Currency get currency => _currency;
  ThemeMode get themeMode => _themeMode;
  bool get isLoading => _isLoading;
  bool get isArabic => _locale.languageCode == 'ar';
  bool get isEnglish => _locale.languageCode == 'en';
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  SettingsProvider() {
    _loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    _setLoading(true);
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load language
      final languageCode = prefs.getString(AppConstants.languageKey);
      if (languageCode != null) {
        _locale = Locale(languageCode);
      }
      
      // Load currency
      final currencyCode = prefs.getString(AppConstants.currencyKey);
      if (currencyCode != null) {
        final currency = CurrencyService.getCurrencyByCode(currencyCode);
        if (currency != null) {
          _currency = currency;
        }
      }
      
      // Load theme mode
      final themeModeString = prefs.getString(AppConstants.themeKey);
      if (themeModeString != null) {
        _themeMode = _parseThemeMode(themeModeString);
      }
      
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Error loading settings: $e');
    }
  }

  // Change language
  Future<void> changeLanguage(Locale newLocale) async {
    if (_locale == newLocale) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.languageKey, newLocale.languageCode);
      
      _locale = newLocale;
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }

  // Toggle language between Arabic and English
  Future<void> toggleLanguage() async {
    final newLocale = _locale.languageCode == 'ar' 
        ? const Locale('en') 
        : const Locale('ar');
    await changeLanguage(newLocale);
  }

  // Change currency
  Future<void> changeCurrency(Currency newCurrency) async {
    if (_currency == newCurrency) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.currencyKey, newCurrency.code);
      
      _currency = newCurrency;
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing currency: $e');
    }
  }

  // Change theme mode
  Future<void> changeThemeMode(ThemeMode newThemeMode) async {
    if (_themeMode == newThemeMode) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.themeKey, newThemeMode.toString());
      
      _themeMode = newThemeMode;
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing theme mode: $e');
    }
  }

  // Toggle theme mode
  Future<void> toggleThemeMode() async {
    ThemeMode newMode;
    switch (_themeMode) {
      case ThemeMode.system:
        newMode = ThemeMode.light;
        break;
      case ThemeMode.light:
        newMode = ThemeMode.dark;
        break;
      case ThemeMode.dark:
        newMode = ThemeMode.system;
        break;
    }
    await changeThemeMode(newMode);
  }

  // Set to light theme
  Future<void> setLightTheme() async {
    await changeThemeMode(ThemeMode.light);
  }

  // Set to dark theme
  Future<void> setDarkTheme() async {
    await changeThemeMode(ThemeMode.dark);
  }

  // Set to system theme
  Future<void> setSystemTheme() async {
    await changeThemeMode(ThemeMode.system);
  }

  // Reset all settings to default
  Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear all settings
      await prefs.remove(AppConstants.languageKey);
      await prefs.remove(AppConstants.currencyKey);
      await prefs.remove(AppConstants.themeKey);
      
      // Reset to defaults
      _locale = LanguageService.defaultLocale;
      _currency = CurrencyService.defaultCurrency;
      _themeMode = ThemeMode.system;
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error resetting settings: $e');
    }
  }

  // Get currency symbol for current language
  String get currencySymbol {
    return CurrencyService.getCurrencySymbol(_currency, isArabic);
  }

  // Get currency name for current language
  String get currencyName {
    return CurrencyService.getCurrencyName(_currency, isArabic);
  }

  // Format amount with current currency
  String formatAmount(double amount) {
    return CurrencyService.formatAmount(amount, _currency, isArabic);
  }

  // Get supported languages
  List<Locale> get supportedLanguages => LanguageService.supportedLocales;

  // Get supported currencies
  List<Currency> get supportedCurrencies => CurrencyService.supportedCurrencies;

  // Get language name
  String getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return isArabic ? 'العربية' : 'Arabic';
      case 'en':
        return isArabic ? 'الإنجليزية' : 'English';
      default:
        return locale.languageCode.toUpperCase();
    }
  }

  // Get theme mode name
  String get themeModeName {
    switch (_themeMode) {
      case ThemeMode.system:
        return isArabic ? 'النظام' : 'System';
      case ThemeMode.light:
        return isArabic ? 'فاتح' : 'Light';
      case ThemeMode.dark:
        return isArabic ? 'داكن' : 'Dark';
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  ThemeMode _parseThemeMode(String themeModeString) {
    switch (themeModeString) {
      case 'ThemeMode.light':
        return ThemeMode.light;
      case 'ThemeMode.dark':
        return ThemeMode.dark;
      case 'ThemeMode.system':
      default:
        return ThemeMode.system;
    }
  }

  // Export settings
  Map<String, dynamic> exportSettings() {
    return {
      'language': _locale.languageCode,
      'currency': _currency.code,
      'themeMode': _themeMode.toString(),
    };
  }

  // Import settings
  Future<void> importSettings(Map<String, dynamic> settings) async {
    try {
      if (settings.containsKey('language')) {
        final languageCode = settings['language'] as String?;
        if (languageCode != null) {
          await changeLanguage(Locale(languageCode));
        }
      }
      
      if (settings.containsKey('currency')) {
        final currencyCode = settings['currency'] as String?;
        if (currencyCode != null) {
          final currency = CurrencyService.getCurrencyByCode(currencyCode);
          if (currency != null) {
            await changeCurrency(currency);
          }
        }
      }
      
      if (settings.containsKey('themeMode')) {
        final themeModeString = settings['themeMode'] as String?;
        if (themeModeString != null) {
          await changeThemeMode(_parseThemeMode(themeModeString));
        }
      }
    } catch (e) {
      debugPrint('Error importing settings: $e');
    }
  }

  // Check if first launch
  Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return !prefs.containsKey(AppConstants.isFirstLaunchKey);
    } catch (e) {
      return true;
    }
  }

  // Mark first launch as complete
  Future<void> markFirstLaunchComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(AppConstants.isFirstLaunchKey, false);
    } catch (e) {
      debugPrint('Error marking first launch complete: $e');
    }
  }
}
