import 'constants.dart';

class Validators {
  // Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'email_required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'invalid_email';
    }
    
    return null;
  }
  
  // Password validation
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'password_required';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'password_too_short';
    }
    
    // Check for at least one letter and one number
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(value)) {
      return 'password_weak';
    }
    
    return null;
  }
  
  // Confirm password validation
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'confirm_password_required';
    }
    
    if (value != password) {
      return 'passwords_not_match';
    }
    
    return null;
  }
  
  // Name validation
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'name_required';
    }
    
    if (value.length > AppConstants.maxNameLength) {
      return 'name_too_long';
    }
    
    // Check for valid characters (letters, spaces, Arabic characters)
    if (!RegExp(r'^[a-zA-Z\u0600-\u06FF\s]+$').hasMatch(value)) {
      return 'invalid_name';
    }
    
    return null;
  }
  
  // Phone number validation
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'phone_required';
    }
    
    // Remove spaces and special characters
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Check for valid phone number (8-15 digits)
    if (!RegExp(r'^\d{8,15}$').hasMatch(cleanPhone)) {
      return 'invalid_phone';
    }
    
    return null;
  }
  
  // Product name validation
  static String? validateProductName(String? value) {
    if (value == null || value.isEmpty) {
      return 'product_name_required';
    }
    
    if (value.length > AppConstants.maxNameLength) {
      return 'product_name_too_long';
    }
    
    return null;
  }
  
  // Price validation
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'price_required';
    }
    
    final price = double.tryParse(value);
    if (price == null) {
      return 'invalid_price';
    }
    
    if (price <= 0) {
      return 'price_must_be_positive';
    }
    
    if (price > 999999.99) {
      return 'price_too_high';
    }
    
    return null;
  }
  
  // Quantity validation
  static String? validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'quantity_required';
    }
    
    final quantity = int.tryParse(value);
    if (quantity == null) {
      return 'invalid_quantity';
    }
    
    if (quantity <= 0) {
      return 'quantity_must_be_positive';
    }
    
    if (quantity > 99999) {
      return 'quantity_too_high';
    }
    
    return null;
  }
  
  // Customer name validation (optional)
  static String? validateCustomerName(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Customer name is optional
    }
    
    if (value.length > AppConstants.maxNameLength) {
      return 'customer_name_too_long';
    }
    
    return null;
  }
  
  // Description validation
  static String? validateDescription(String? value) {
    if (value != null && value.length > AppConstants.maxDescriptionLength) {
      return 'description_too_long';
    }
    
    return null;
  }
  
  // Required field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '${fieldName}_required';
    }
    
    return null;
  }
  
  // Numeric validation
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '${fieldName}_required';
    }
    
    if (double.tryParse(value) == null) {
      return 'invalid_$fieldName';
    }
    
    return null;
  }
  
  // Date validation
  static String? validateDate(DateTime? value) {
    if (value == null) {
      return 'date_required';
    }
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDate = DateTime(value.year, value.month, value.day);
    
    if (selectedDate.isAfter(today)) {
      return 'date_cannot_be_future';
    }
    
    // Check if date is too old (more than 1 year)
    final oneYearAgo = today.subtract(const Duration(days: 365));
    if (selectedDate.isBefore(oneYearAgo)) {
      return 'date_too_old';
    }
    
    return null;
  }
}
