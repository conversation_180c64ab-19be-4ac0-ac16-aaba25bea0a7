import 'package:flutter/material.dart';
import '../models/sale_model.dart';
import '../models/user_model.dart';
import '../services/firestore_service.dart';
import '../utils/date_helpers.dart';

class SalesProvider extends ChangeNotifier {
  List<Sale> _sales = [];
  List<Sale> _filteredSales = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  // Filter options
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedUserId;
  String? _selectedCategory;
  String _searchQuery = '';
  
  // Statistics
  double _totalSales = 0.0;
  int _totalTransactions = 0;
  double _averageTransaction = 0.0;
  Map<String, double> _salesByCategory = {};
  Map<String, double> _salesByEmployee = {};

  // Getters
  List<Sale> get sales => _filteredSales;
  List<Sale> get allSales => _sales;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String? get selectedUserId => _selectedUserId;
  String? get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  double get totalSales => _totalSales;
  int get totalTransactions => _totalTransactions;
  double get averageTransaction => _averageTransaction;
  Map<String, double> get salesByCategory => _salesByCategory;
  Map<String, double> get salesByEmployee => _salesByEmployee;

  // Load sales data
  Future<void> loadSales({String? userId, bool forceRefresh = false}) async {
    if (_isLoading && !forceRefresh) return;
    
    _setLoading(true);
    _clearError();

    try {
      List<Sale> loadedSales;
      
      if (userId != null) {
        // Load sales for specific user
        loadedSales = await FirestoreService.getUserSales(userId);
      } else {
        // Load all sales (admin only)
        loadedSales = await FirestoreService.getAllSales();
      }

      _sales = loadedSales;
      _applyFilters();
      _calculateStatistics();
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load sales: $e');
      _setLoading(false);
    }
  }

  // Load sales by date range
  Future<void> loadSalesByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final loadedSales = await FirestoreService.getSalesByDateRange(
        startDate: startDate,
        endDate: endDate,
        userId: userId,
      );

      _sales = loadedSales;
      _startDate = startDate;
      _endDate = endDate;
      _selectedUserId = userId;
      
      _applyFilters();
      _calculateStatistics();
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load sales: $e');
      _setLoading(false);
    }
  }

  // Add new sale
  Future<bool> addSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      final saleId = await FirestoreService.createSale(sale);
      final newSale = sale.copyWith(id: saleId);
      
      _sales.insert(0, newSale);
      _applyFilters();
      _calculateStatistics();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('Failed to add sale: $e');
      _setLoading(false);
      return false;
    }
  }

  // Update sale
  Future<bool> updateSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await FirestoreService.updateSale(sale);
      
      final index = _sales.indexWhere((s) => s.id == sale.id);
      if (index != -1) {
        _sales[index] = sale;
        _applyFilters();
        _calculateStatistics();
      }
      
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('Failed to update sale: $e');
      _setLoading(false);
      return false;
    }
  }

  // Delete sale
  Future<bool> deleteSale(String saleId) async {
    _setLoading(true);
    _clearError();

    try {
      await FirestoreService.deleteSale(saleId);
      
      _sales.removeWhere((sale) => sale.id == saleId);
      _applyFilters();
      _calculateStatistics();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('Failed to delete sale: $e');
      _setLoading(false);
      return false;
    }
  }

  // Apply filters
  void _applyFilters() {
    _filteredSales = _sales.where((sale) {
      // Date filter
      if (_startDate != null && sale.date.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && sale.date.isAfter(_endDate!)) {
        return false;
      }
      
      // User filter
      if (_selectedUserId != null && sale.userId != _selectedUserId) {
        return false;
      }
      
      // Category filter
      if (_selectedCategory != null && sale.category != _selectedCategory) {
        return false;
      }
      
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return sale.productName.toLowerCase().contains(query) ||
               (sale.customerName?.toLowerCase().contains(query) ?? false) ||
               (sale.category?.toLowerCase().contains(query) ?? false);
      }
      
      return true;
    }).toList();
    
    // Sort by date (newest first)
    _filteredSales.sort((a, b) => b.date.compareTo(a.date));
  }

  // Calculate statistics
  void _calculateStatistics() {
    _totalSales = 0.0;
    _totalTransactions = _filteredSales.length;
    _salesByCategory.clear();
    _salesByEmployee.clear();

    for (final sale in _filteredSales) {
      _totalSales += sale.totalPrice;
      
      // Group by category
      final category = sale.category ?? 'Other';
      _salesByCategory[category] = (_salesByCategory[category] ?? 0) + sale.totalPrice;
      
      // Group by employee
      final employee = sale.userName;
      _salesByEmployee[employee] = (_salesByEmployee[employee] ?? 0) + sale.totalPrice;
    }

    _averageTransaction = _totalTransactions > 0 ? _totalSales / _totalTransactions : 0.0;
  }

  // Set date filter
  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    _applyFilters();
    _calculateStatistics();
    notifyListeners();
  }

  // Set user filter
  void setUserFilter(String? userId) {
    _selectedUserId = userId;
    _applyFilters();
    _calculateStatistics();
    notifyListeners();
  }

  // Set category filter
  void setCategoryFilter(String? category) {
    _selectedCategory = category;
    _applyFilters();
    _calculateStatistics();
    notifyListeners();
  }

  // Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    _calculateStatistics();
    notifyListeners();
  }

  // Clear all filters
  void clearFilters() {
    _startDate = null;
    _endDate = null;
    _selectedUserId = null;
    _selectedCategory = null;
    _searchQuery = '';
    _applyFilters();
    _calculateStatistics();
    notifyListeners();
  }

  // Get sales for today
  List<Sale> get todaySales {
    final today = DateTime.now();
    return _sales.where((sale) => DateHelpers.isSameDay(sale.date, today)).toList();
  }

  // Get sales for this week
  List<Sale> get thisWeekSales {
    return _sales.where((sale) => DateHelpers.isThisWeek(sale.date)).toList();
  }

  // Get sales for this month
  List<Sale> get thisMonthSales {
    return _sales.where((sale) => DateHelpers.isThisMonth(sale.date)).toList();
  }

  // Get top selling categories
  List<MapEntry<String, double>> get topCategories {
    final entries = _salesByCategory.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  // Get top performing employees
  List<MapEntry<String, double>> get topEmployees {
    final entries = _salesByEmployee.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  // Get unique categories
  List<String> get categories {
    final categories = _sales
        .map((sale) => sale.category ?? 'Other')
        .toSet()
        .toList();
    categories.sort();
    return categories;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh({String? userId}) async {
    await loadSales(userId: userId, forceRefresh: true);
  }
}
