import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'services/language_service.dart';
import 'services/currency_service.dart';
import 'widgets/currency_picker.dart';

void main() {
  runApp(const SalesTrackerApp());
}

class SalesTrackerApp extends StatefulWidget {
  const SalesTrackerApp({super.key});

  @override
  State<SalesTrackerApp> createState() => _SalesTrackerAppState();
}

class _SalesTrackerAppState extends State<SalesTrackerApp> {
  Locale _locale = LanguageService.defaultLocale;
  Currency _currency = CurrencyService.defaultCurrency;

  @override
  void initState() {
    super.initState();
    _loadSavedPreferences();
  }

  Future<void> _loadSavedPreferences() async {
    final savedLocale = await LanguageService.getSavedLanguage();
    final savedCurrency = await CurrencyService.getSavedCurrency();
    setState(() {
      _locale = savedLocale;
      _currency = savedCurrency;
    });
  }

  void _changeLanguage(Locale locale) async {
    await LanguageService.saveLanguage(locale);
    setState(() {
      _locale = locale;
    });
  }

  void _changeCurrency(Currency currency) async {
    await CurrencyService.saveCurrency(currency);
    setState(() {
      _currency = currency;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Sales Tracker',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LanguageService.supportedLocales,
      home: SalesTrackerHomePage(
        onLanguageChanged: _changeLanguage,
        onCurrencyChanged: _changeCurrency,
        selectedCurrency: _currency,
      ),
    );
  }
}

class SalesTrackerHomePage extends StatefulWidget {
  const SalesTrackerHomePage({
    super.key,
    required this.onLanguageChanged,
    required this.onCurrencyChanged,
    required this.selectedCurrency,
  });

  final Function(Locale) onLanguageChanged;
  final Function(Currency) onCurrencyChanged;
  final Currency selectedCurrency;

  @override
  State<SalesTrackerHomePage> createState() => _SalesTrackerHomePageState();
}

class _SalesTrackerHomePageState extends State<SalesTrackerHomePage> {
  double _totalSales = 0.0;
  final List<Sale> _sales = [];

  void _addSale() {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        String productName = '';
        double amount = 0.0;

        return AlertDialog(
          title: Text(localizations.addNewSale),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: InputDecoration(
                  labelText: localizations.productName,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) => productName = value,
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: InputDecoration(
                  labelText: localizations.amount,
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) => amount = double.tryParse(value) ?? 0.0,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(localizations.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                if (productName.isNotEmpty && amount > 0) {
                  setState(() {
                    _sales.add(
                      Sale(
                        productName: productName,
                        amount: amount,
                        date: DateTime.now(),
                      ),
                    );
                    _totalSales += amount;
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Text(localizations.add),
            ),
          ],
        );
      },
    );
  }

  void _toggleLanguage() {
    final currentLocale = Localizations.localeOf(context);
    final newLocale = LanguageService.getOppositeLanguage(currentLocale);
    widget.onLanguageChanged(newLocale);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(localizations.homeTitle),
        centerTitle: true,
        actions: [
          CurrencySelectionButton(
            selectedCurrency: widget.selectedCurrency,
            onCurrencyChanged: widget.onCurrencyChanged,
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _toggleLanguage,
            icon: const Icon(Icons.language),
            tooltip: localizations.language,
          ),
        ],
      ),
      body: Column(
        children: [
          // Total Sales Section
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              children: [
                Text(
                  localizations.totalSales,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  CurrencyService.formatAmount(
                    _totalSales,
                    widget.selectedCurrency,
                    isArabic,
                  ),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ),

          // Sales List
          Expanded(
            child:
                _sales.isEmpty
                    ? Center(
                      child: Text(
                        localizations.noSalesYet,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _sales.length,
                      itemBuilder: (context, index) {
                        final sale = _sales[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: const Icon(
                              Icons.shopping_cart,
                              color: Colors.blue,
                            ),
                            title: Text(sale.productName),
                            subtitle: Text(
                              '${sale.date.day}/${sale.date.month}/${sale.date.year}',
                            ),
                            trailing: Text(
                              CurrencyService.formatAmount(
                                sale.amount,
                                widget.selectedCurrency,
                                isArabic,
                              ),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addSale,
        tooltip: localizations.addSale,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class Sale {
  final String productName;
  final double amount;
  final DateTime date;

  Sale({required this.productName, required this.amount, required this.date});
}
