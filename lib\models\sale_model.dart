import 'package:cloud_firestore/cloud_firestore.dart';

class Sale {
  final String id;
  final String userId;
  final String userName;
  final String productName;
  final String? productId;
  final String? category;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? customerName;
  final String? customerPhone;
  final String currency;
  final DateTime date;
  final String? notes;
  final String? imageUrl;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Sale({
    required this.id,
    required this.userId,
    required this.userName,
    required this.productName,
    this.productId,
    this.category,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.customerName,
    this.customerPhone,
    required this.currency,
    required this.date,
    this.notes,
    this.imageUrl,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  });

  // Factory constructor from Firestore document
  factory Sale.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Sale(
      id: doc.id,
      userId: data['userId'] ?? '',
      userName: data['userName'] ?? '',
      productName: data['productName'] ?? '',
      productId: data['productId'],
      category: data['category'],
      quantity: data['quantity'] ?? 1,
      unitPrice: (data['unitPrice'] ?? 0.0).toDouble(),
      totalPrice: (data['totalPrice'] ?? 0.0).toDouble(),
      customerName: data['customerName'],
      customerPhone: data['customerPhone'],
      currency: data['currency'] ?? 'BHD',
      date: (data['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notes: data['notes'],
      imageUrl: data['imageUrl'],
      metadata: data['metadata'] as Map<String, dynamic>?,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  // Factory constructor from Map
  factory Sale.fromMap(Map<String, dynamic> map, String id) {
    return Sale(
      id: id,
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      productName: map['productName'] ?? '',
      productId: map['productId'],
      category: map['category'],
      quantity: map['quantity'] ?? 1,
      unitPrice: (map['unitPrice'] ?? 0.0).toDouble(),
      totalPrice: (map['totalPrice'] ?? 0.0).toDouble(),
      customerName: map['customerName'],
      customerPhone: map['customerPhone'],
      currency: map['currency'] ?? 'BHD',
      date: (map['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notes: map['notes'],
      imageUrl: map['imageUrl'],
      metadata: map['metadata'] as Map<String, dynamic>?,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'productName': productName,
      'productId': productId,
      'category': category,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'currency': currency,
      'date': Timestamp.fromDate(date),
      'notes': notes,
      'imageUrl': imageUrl,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  // Copy with method
  Sale copyWith({
    String? id,
    String? userId,
    String? userName,
    String? productName,
    String? productId,
    String? category,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    String? customerName,
    String? customerPhone,
    String? currency,
    DateTime? date,
    String? notes,
    String? imageUrl,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Sale(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      productName: productName ?? this.productName,
      productId: productId ?? this.productId,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      currency: currency ?? this.currency,
      date: date ?? this.date,
      notes: notes ?? this.notes,
      imageUrl: imageUrl ?? this.imageUrl,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  double get profit {
    // This would require cost price to calculate actual profit
    // For now, we'll assume a default profit margin
    return totalPrice * 0.2; // 20% profit margin
  }

  bool get hasCustomer => customerName != null && customerName!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get isUpdated => updatedAt != null;

  String get displayCustomer => customerName ?? 'Walk-in Customer';
  String get displayCategory => category ?? 'General';

  // Calculate total with tax (if needed)
  double getTotalWithTax([double taxRate = 0.0]) {
    return totalPrice * (1 + taxRate);
  }

  // Get discount amount
  double getDiscountAmount(double discountPercentage) {
    return totalPrice * (discountPercentage / 100);
  }

  // Apply discount
  Sale applyDiscount(double discountPercentage) {
    final discountAmount = getDiscountAmount(discountPercentage);
    final newTotal = totalPrice - discountAmount;
    return copyWith(
      totalPrice: newTotal,
      updatedAt: DateTime.now(),
    );
  }

  // Update quantity and recalculate total
  Sale updateQuantity(int newQuantity) {
    final newTotal = unitPrice * newQuantity;
    return copyWith(
      quantity: newQuantity,
      totalPrice: newTotal,
      updatedAt: DateTime.now(),
    );
  }

  // Update unit price and recalculate total
  Sale updateUnitPrice(double newUnitPrice) {
    final newTotal = newUnitPrice * quantity;
    return copyWith(
      unitPrice: newUnitPrice,
      totalPrice: newTotal,
      updatedAt: DateTime.now(),
    );
  }

  // Mark as updated
  Sale markAsUpdated() {
    return copyWith(updatedAt: DateTime.now());
  }

  // Get metadata value
  T? getMetadata<T>(String key, [T? defaultValue]) {
    if (metadata == null) return defaultValue;
    return metadata![key] as T? ?? defaultValue;
  }

  // Set metadata value
  Sale setMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata[key] = value;
    return copyWith(
      metadata: newMetadata,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Sale(id: $id, productName: $productName, quantity: $quantity, totalPrice: $totalPrice, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Sale && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Sale status enum
enum SaleStatus {
  pending,
  completed,
  cancelled,
  refunded;

  bool get isPending => this == SaleStatus.pending;
  bool get isCompleted => this == SaleStatus.completed;
  bool get isCancelled => this == SaleStatus.cancelled;
  bool get isRefunded => this == SaleStatus.refunded;
}

// Payment method enum
enum PaymentMethod {
  cash,
  card,
  transfer,
  other;

  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.transfer:
        return 'Bank Transfer';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}
