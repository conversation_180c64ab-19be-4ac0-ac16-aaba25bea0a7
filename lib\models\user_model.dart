import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/constants.dart';

class UserModel {
  final String id;
  final String name;
  final String email;
  final String role;
  final String? department;
  final String? phone;
  final String? profileImageUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? preferences;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.department,
    this.phone,
    this.profileImageUrl,
    this.isActive = true,
    required this.createdAt,
    this.lastLoginAt,
    this.preferences,
  });

  // Factory constructor from Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserModel(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? AppConstants.roleEmployee,
      department: data['department'],
      phone: data['phone'],
      profileImageUrl: data['profileImageUrl'],
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLoginAt: (data['lastLoginAt'] as Timestamp?)?.toDate(),
      preferences: data['preferences'] as Map<String, dynamic>?,
    );
  }

  // Factory constructor from Map
  factory UserModel.fromMap(Map<String, dynamic> map, String id) {
    return UserModel(
      id: id,
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      role: map['role'] ?? AppConstants.roleEmployee,
      department: map['department'],
      phone: map['phone'],
      profileImageUrl: map['profileImageUrl'],
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLoginAt: (map['lastLoginAt'] as Timestamp?)?.toDate(),
      preferences: map['preferences'] as Map<String, dynamic>?,
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'role': role,
      'department': department,
      'phone': phone,
      'profileImageUrl': profileImageUrl,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
      'preferences': preferences,
    };
  }

  // Copy with method
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? department,
    String? phone,
    String? profileImageUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      department: department ?? this.department,
      phone: phone ?? this.phone,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
    );
  }

  // Helper methods
  bool get isAdmin => role == AppConstants.roleAdmin;
  bool get isEmployee => role == AppConstants.roleEmployee;
  
  String get displayName => name.isNotEmpty ? name : email.split('@').first;
  
  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names.first.substring(0, 1).toUpperCase();
    }
    return email.substring(0, 1).toUpperCase();
  }

  // Get preference value
  T? getPreference<T>(String key, [T? defaultValue]) {
    if (preferences == null) return defaultValue;
    return preferences![key] as T? ?? defaultValue;
  }

  // Set preference value
  UserModel setPreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences ?? {});
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  // Remove preference
  UserModel removePreference(String key) {
    if (preferences == null) return this;
    final newPreferences = Map<String, dynamic>.from(preferences!);
    newPreferences.remove(key);
    return copyWith(preferences: newPreferences);
  }

  // Update last login
  UserModel updateLastLogin() {
    return copyWith(lastLoginAt: DateTime.now());
  }

  // Activate/Deactivate user
  UserModel activate() => copyWith(isActive: true);
  UserModel deactivate() => copyWith(isActive: false);

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// User role enum for better type safety
enum UserRole {
  admin(AppConstants.roleAdmin),
  employee(AppConstants.roleEmployee);

  const UserRole(this.value);
  final String value;

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case AppConstants.roleAdmin:
        return UserRole.admin;
      case AppConstants.roleEmployee:
        return UserRole.employee;
      default:
        return UserRole.employee;
    }
  }
}

// User status enum
enum UserStatus {
  active,
  inactive,
  pending,
  suspended;

  bool get isActive => this == UserStatus.active;
  bool get isInactive => this == UserStatus.inactive;
  bool get isPending => this == UserStatus.pending;
  bool get isSuspended => this == UserStatus.suspended;
}
