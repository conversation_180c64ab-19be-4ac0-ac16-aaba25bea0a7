import 'package:shared_preferences/shared_preferences.dart';

/// نموذج العملة
class Currency {
  final String code;
  final String nameAr;
  final String nameEn;
  final String symbolAr;
  final String symbolEn;
  final String country;
  final String flag;

  const Currency({
    required this.code,
    required this.nameAr,
    required this.nameEn,
    required this.symbolAr,
    required this.symbolEn,
    required this.country,
    required this.flag,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Currency && runtimeType == other.runtimeType && code == other.code;

  @override
  int get hashCode => code.hashCode;
}

/// خدمة إدارة العملات
class CurrencyService {
  static const String _currencyKey = 'selected_currency';
  
  /// العملات المدعومة
  static const List<Currency> supportedCurrencies = [
    Currency(
      code: 'BHD',
      nameAr: 'دينار بحريني',
      nameEn: 'Bahraini Dinar',
      symbolAr: 'د.ب',
      symbolEn: 'BHD',
      country: 'البحرين',
      flag: '🇧🇭',
    ),
    Currency(
      code: 'SAR',
      nameAr: 'ريال سعودي',
      nameEn: 'Saudi Riyal',
      symbolAr: 'ريال',
      symbolEn: 'SAR',
      country: 'السعودية',
      flag: '🇸🇦',
    ),
    Currency(
      code: 'USD',
      nameAr: 'دولار أمريكي',
      nameEn: 'US Dollar',
      symbolAr: 'دولار',
      symbolEn: 'USD',
      country: 'أمريكا',
      flag: '🇺🇸',
    ),
    Currency(
      code: 'EUR',
      nameAr: 'يورو',
      nameEn: 'Euro',
      symbolAr: 'يورو',
      symbolEn: 'EUR',
      country: 'أوروبا',
      flag: '🇪🇺',
    ),
    Currency(
      code: 'AED',
      nameAr: 'درهم إماراتي',
      nameEn: 'UAE Dirham',
      symbolAr: 'درهم',
      symbolEn: 'AED',
      country: 'الإمارات',
      flag: '🇦🇪',
    ),
    Currency(
      code: 'KWD',
      nameAr: 'دينار كويتي',
      nameEn: 'Kuwaiti Dinar',
      symbolAr: 'د.ك',
      symbolEn: 'KWD',
      country: 'الكويت',
      flag: '🇰🇼',
    ),
    Currency(
      code: 'QAR',
      nameAr: 'ريال قطري',
      nameEn: 'Qatari Riyal',
      symbolAr: 'ريال قطري',
      symbolEn: 'QAR',
      country: 'قطر',
      flag: '🇶🇦',
    ),
    Currency(
      code: 'OMR',
      nameAr: 'ريال عماني',
      nameEn: 'Omani Rial',
      symbolAr: 'ريال عماني',
      symbolEn: 'OMR',
      country: 'عمان',
      flag: '🇴🇲',
    ),
  ];
  
  /// العملة الافتراضية (الدينار البحريني)
  static const Currency defaultCurrency = Currency(
    code: 'BHD',
    nameAr: 'دينار بحريني',
    nameEn: 'Bahraini Dinar',
    symbolAr: 'د.ب',
    symbolEn: 'BHD',
    country: 'البحرين',
    flag: '🇧🇭',
  );
  
  /// الحصول على العملة المحفوظة
  static Future<Currency> getSavedCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    final currencyCode = prefs.getString(_currencyKey);
    
    if (currencyCode != null) {
      return getCurrencyByCode(currencyCode) ?? defaultCurrency;
    }
    
    return defaultCurrency;
  }
  
  /// حفظ العملة المختارة
  static Future<void> saveCurrency(Currency currency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currencyKey, currency.code);
  }
  
  /// البحث عن العملة بالرمز
  static Currency? getCurrencyByCode(String code) {
    try {
      return supportedCurrencies.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }
  
  /// تنسيق المبلغ مع العملة
  static String formatAmount(double amount, Currency currency, bool isArabic) {
    final formattedAmount = amount.toStringAsFixed(2);
    final symbol = isArabic ? currency.symbolAr : currency.symbolEn;
    
    if (isArabic) {
      return '$formattedAmount $symbol';
    } else {
      return '$formattedAmount $symbol';
    }
  }
  
  /// الحصول على اسم العملة حسب اللغة
  static String getCurrencyName(Currency currency, bool isArabic) {
    return isArabic ? currency.nameAr : currency.nameEn;
  }
  
  /// الحصول على رمز العملة حسب اللغة
  static String getCurrencySymbol(Currency currency, bool isArabic) {
    return isArabic ? currency.symbolAr : currency.symbolEn;
  }
}
