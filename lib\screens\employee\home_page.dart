import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../providers/sales_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/navigation/app_drawer.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final authProvider = context.read<AuthProvider>();
    final salesProvider = context.read<SalesProvider>();

    if (authProvider.isAuthenticated) {
      await salesProvider.loadSales(userId: authProvider.userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final settingsProvider = context.watch<SettingsProvider>();
    final authProvider = context.watch<AuthProvider>();
    final salesProvider = context.watch<SalesProvider>();
    final isArabic = settingsProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.homeTitle),
        centerTitle: true,
        actions: [
          // Currency Selection
          IconButton(
            onPressed: () {
              _showCurrencySelector(context);
            },
            icon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  settingsProvider.currencySymbol,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Icon(Icons.arrow_drop_down, size: 16),
              ],
            ),
            tooltip: isArabic ? 'اختيار العملة' : 'Select Currency',
          ),
          // Language Toggle
          IconButton(
            onPressed: settingsProvider.toggleLanguage,
            icon: const Icon(Icons.language),
            tooltip: isArabic ? 'تغيير اللغة' : 'Change Language',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child:
            salesProvider.isLoading
                ? const Center(child: LoadingWidget())
                : SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome Section
                      _buildWelcomeSection(context, authProvider, isArabic),

                      const SizedBox(height: 24),

                      // Statistics Cards
                      _buildStatisticsSection(
                        context,
                        salesProvider,
                        settingsProvider,
                        isArabic,
                      ),

                      const SizedBox(height: 24),

                      // Quick Actions
                      _buildQuickActionsSection(context, isArabic),

                      const SizedBox(height: 24),

                      // Recent Sales
                      _buildRecentSalesSection(
                        context,
                        salesProvider,
                        settingsProvider,
                        isArabic,
                      ),
                    ],
                  ),
                ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Navigate to add sale page
          context.push('/home/<USER>');
        },
        icon: const Icon(Icons.add),
        label: Text(isArabic ? 'إضافة مبيعة' : 'Add Sale'),
      ),
    );
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    AuthProvider authProvider,
    bool isArabic,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                authProvider.userModel?.initials ?? 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isArabic
                        ? 'مرحباً، ${authProvider.displayName}'
                        : 'Welcome, ${authProvider.displayName}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isArabic
                        ? 'لنبدأ يوماً مثمراً في المبيعات!'
                        : 'Let\'s have a productive sales day!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(
    BuildContext context,
    SalesProvider salesProvider,
    SettingsProvider settingsProvider,
    bool isArabic,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'إحصائيات اليوم' : 'Today\'s Statistics',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                title: isArabic ? 'إجمالي المبيعات' : 'Total Sales',
                value: settingsProvider.formatAmount(salesProvider.totalSales),
                icon: Icons.trending_up,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                title: isArabic ? 'عدد المعاملات' : 'Transactions',
                value: salesProvider.totalTransactions.toString(),
                icon: Icons.receipt_long,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                title: isArabic ? 'متوسط المعاملة' : 'Avg Transaction',
                value: settingsProvider.formatAmount(
                  salesProvider.averageTransaction,
                ),
                icon: Icons.analytics,
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                context,
                title: isArabic ? 'مبيعات الأسبوع' : 'This Week',
                value: salesProvider.thisWeekSales.length.toString(),
                icon: Icons.date_range,
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection(BuildContext context, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'إجراءات سريعة' : 'Quick Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context,
                title: isArabic ? 'إضافة مبيعة' : 'Add Sale',
                icon: Icons.add_shopping_cart,
                color: Colors.green,
                onTap: () => context.push('/home/<USER>'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                context,
                title: isArabic ? 'مبيعاتي' : 'My Sales',
                icon: Icons.list_alt,
                color: Colors.blue,
                onTap: () => context.push('/home/<USER>'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentSalesSection(
    BuildContext context,
    SalesProvider salesProvider,
    SettingsProvider settingsProvider,
    bool isArabic,
  ) {
    final recentSales = salesProvider.sales.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              isArabic ? 'المبيعات الأخيرة' : 'Recent Sales',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => context.push('/home/<USER>'),
              child: Text(isArabic ? 'عرض الكل' : 'View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (recentSales.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    isArabic ? 'لا توجد مبيعات بعد' : 'No sales yet',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isArabic
                        ? 'ابدأ بإضافة أول مبيعة لك!'
                        : 'Start by adding your first sale!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )
        else
          ...recentSales
              .map(
                (sale) => Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          Theme.of(context).colorScheme.primaryContainer,
                      child: Icon(
                        Icons.shopping_bag,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    title: Text(sale.productName),
                    subtitle: Text(
                      '${sale.quantity} × ${settingsProvider.formatAmount(sale.unitPrice)}',
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          settingsProvider.formatAmount(sale.totalPrice),
                          style: Theme.of(
                            context,
                          ).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                        Text(
                          '${sale.date.day}/${sale.date.month}',
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    onTap: () => context.push('/home/<USER>/${sale.id}'),
                  ),
                ),
              )
              .toList(),
      ],
    );
  }

  void _showCurrencySelector(BuildContext context) {
    // This would show a currency selection dialog
    // For now, we'll just show a simple message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Currency selector - Coming Soon')),
    );
  }
}
