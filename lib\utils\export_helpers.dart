import 'dart:io';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/sale_model.dart';
import '../models/user_model.dart';
import 'date_helpers.dart';
import 'constants.dart';

class ExportHelpers {
  // Export sales to CSV
  static Future<String?> exportSalesToCSV(
    List<Sale> sales, {
    String? fileName,
    bool isArabic = false,
  }) async {
    try {
      // Request storage permission
      if (!await _requestStoragePermission()) {
        return null;
      }

      // Prepare CSV data
      final List<List<String>> csvData = [];
      
      // Headers
      if (isArabic) {
        csvData.add([
          'التاريخ',
          'اسم المنتج',
          'الكمية',
          'سعر الوحدة',
          'المجموع',
          'اسم العميل',
          'الموظف',
          'العملة'
        ]);
      } else {
        csvData.add([
          'Date',
          'Product Name',
          'Quantity',
          'Unit Price',
          'Total Price',
          'Customer Name',
          'Employee',
          'Currency'
        ]);
      }

      // Data rows
      for (final sale in sales) {
        csvData.add([
          DateHelpers.formatDate(sale.date),
          sale.productName,
          sale.quantity.toString(),
          sale.unitPrice.toStringAsFixed(2),
          sale.totalPrice.toStringAsFixed(2),
          sale.customerName ?? '',
          sale.userName ?? '',
          sale.currency,
        ]);
      }

      // Convert to CSV string
      final csvString = const ListToCsvConverter().convert(csvData);

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${fileName ?? 'sales_export_${DateTime.now().millisecondsSinceEpoch}.csv'}');
      await file.writeAsString(csvString);

      return file.path;
    } catch (e) {
      return null;
    }
  }

  // Export sales to PDF
  static Future<String?> exportSalesToPDF(
    List<Sale> sales, {
    String? fileName,
    bool isArabic = false,
    String? title,
  }) async {
    try {
      // Request storage permission
      if (!await _requestStoragePermission()) {
        return null;
      }

      final pdf = pw.Document();

      // Add page
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              // Title
              pw.Header(
                level: 0,
                child: pw.Text(
                  title ?? (isArabic ? 'تقرير المبيعات' : 'Sales Report'),
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // Date range
              pw.Text(
                '${isArabic ? 'تاريخ التقرير:' : 'Report Date:'} ${DateHelpers.formatDateTime(DateTime.now())}',
                style: const pw.TextStyle(fontSize: 12),
              ),
              
              pw.SizedBox(height: 20),
              
              // Sales table
              pw.Table(
                border: pw.TableBorder.all(),
                columnWidths: {
                  0: const pw.FixedColumnWidth(80),
                  1: const pw.FlexColumnWidth(2),
                  2: const pw.FixedColumnWidth(60),
                  3: const pw.FixedColumnWidth(80),
                  4: const pw.FixedColumnWidth(80),
                  5: const pw.FlexColumnWidth(1.5),
                },
                children: [
                  // Header row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey300,
                    ),
                    children: [
                      _buildTableCell(isArabic ? 'التاريخ' : 'Date', isHeader: true),
                      _buildTableCell(isArabic ? 'المنتج' : 'Product', isHeader: true),
                      _buildTableCell(isArabic ? 'الكمية' : 'Qty', isHeader: true),
                      _buildTableCell(isArabic ? 'السعر' : 'Price', isHeader: true),
                      _buildTableCell(isArabic ? 'المجموع' : 'Total', isHeader: true),
                      _buildTableCell(isArabic ? 'العميل' : 'Customer', isHeader: true),
                    ],
                  ),
                  
                  // Data rows
                  ...sales.map((sale) => pw.TableRow(
                    children: [
                      _buildTableCell(DateHelpers.formatDate(sale.date)),
                      _buildTableCell(sale.productName),
                      _buildTableCell(sale.quantity.toString()),
                      _buildTableCell('${sale.unitPrice.toStringAsFixed(2)} ${sale.currency}'),
                      _buildTableCell('${sale.totalPrice.toStringAsFixed(2)} ${sale.currency}'),
                      _buildTableCell(sale.customerName ?? ''),
                    ],
                  )).toList(),
                ],
              ),
              
              pw.SizedBox(height: 20),
              
              // Summary
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(),
                  color: PdfColors.grey100,
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      isArabic ? 'ملخص التقرير:' : 'Report Summary:',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text('${isArabic ? 'عدد المبيعات:' : 'Total Sales:'} ${sales.length}'),
                    pw.Text('${isArabic ? 'إجمالي المبلغ:' : 'Total Amount:'} ${_calculateTotalAmount(sales).toStringAsFixed(2)}'),
                  ],
                ),
              ),
            ];
          },
        ),
      );

      // Save to file
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${fileName ?? 'sales_report_${DateTime.now().millisecondsSinceEpoch}.pdf'}');
      await file.writeAsBytes(await pdf.save());

      return file.path;
    } catch (e) {
      return null;
    }
  }

  // Helper method to build table cell
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // Calculate total amount
  static double _calculateTotalAmount(List<Sale> sales) {
    return sales.fold(0.0, (sum, sale) => sum + sale.totalPrice);
  }

  // Request storage permission
  static Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    }
    return true; // iOS doesn't need explicit storage permission for app documents
  }

  // Export users to CSV
  static Future<String?> exportUsersToCSV(
    List<UserModel> users, {
    String? fileName,
    bool isArabic = false,
  }) async {
    try {
      if (!await _requestStoragePermission()) {
        return null;
      }

      final List<List<String>> csvData = [];
      
      // Headers
      if (isArabic) {
        csvData.add(['الاسم', 'البريد الإلكتروني', 'الدور', 'القسم', 'تاريخ التسجيل']);
      } else {
        csvData.add(['Name', 'Email', 'Role', 'Department', 'Registration Date']);
      }

      // Data rows
      for (final user in users) {
        csvData.add([
          user.name,
          user.email,
          user.role,
          user.department ?? '',
          DateHelpers.formatDate(user.createdAt),
        ]);
      }

      final csvString = const ListToCsvConverter().convert(csvData);
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${fileName ?? 'users_export_${DateTime.now().millisecondsSinceEpoch}.csv'}');
      await file.writeAsString(csvString);

      return file.path;
    } catch (e) {
      return null;
    }
  }

  // Get export directory
  static Future<Directory> getExportDirectory() async {
    if (Platform.isAndroid) {
      return await getExternalStorageDirectory() ?? await getApplicationDocumentsDirectory();
    } else {
      return await getApplicationDocumentsDirectory();
    }
  }

  // Share file
  static Future<void> shareFile(String filePath) async {
    // This would typically use a sharing plugin like share_plus
    // For now, we'll just show the file path
    print('File saved at: $filePath');
  }
}
