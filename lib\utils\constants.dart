class AppConstants {
  // App Info
  static const String appName = 'Daily Sales Tracker';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Professional sales management system';

  // User Roles
  static const String roleEmployee = 'employee';
  static const String roleAdmin = 'admin';

  // Collection Names (Firestore)
  static const String usersCollection = 'users';
  static const String salesCollection = 'sales';
  static const String productsCollection = 'products';
  static const String reportsCollection = 'reports';
  static const String categoriesCollection = 'categories';

  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String productImagesPath = 'product_images';
  static const String reportsPath = 'reports';

  // Shared Preferences Keys
  static const String languageKey = 'selected_language';
  static const String currencyKey = 'selected_currency';
  static const String themeKey = 'theme_mode';
  static const String userRoleKey = 'user_role';
  static const String isFirstLaunchKey = 'is_first_launch';

  // Default Values
  static const String defaultLanguage = 'ar';
  static const String defaultCurrency = 'BHD';
  static const int defaultPageSize = 20;
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Chart Colors
  static const List<String> chartColors = [
    '#FF6B6B',
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
    '#85C1E9',
  ];

  // Error Messages
  static const String networkError = 'network_error';
  static const String unknownError = 'unknown_error';
  static const String authError = 'auth_error';
  static const String permissionError = 'permission_error';

  // Success Messages
  static const String saveSuccess = 'save_success';
  static const String deleteSuccess = 'delete_success';
  static const String updateSuccess = 'update_success';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';

  // Export Formats
  static const String pdfFormat = 'pdf';
  static const String csvFormat = 'csv';
  static const String excelFormat = 'xlsx';

  // Notification Types
  static const String dailyReminder = 'daily_reminder';
  static const String weeklyReport = 'weekly_report';
  static const String monthlyReport = 'monthly_report';
  static const String lowStock = 'low_stock';

  // Routes
  static const String splashRoute = '/';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String forgotPasswordRoute = '/forgot-password';
  static const String homeRoute = '/home';
  static const String adminDashboardRoute = '/admin';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
  static const String addSaleRoute = '/add-sale';
  static const String mySalesRoute = '/my-sales';
  static const String allSalesRoute = '/all-sales';
  static const String reportsRoute = '/reports';
  static const String manageUsersRoute = '/manage-users';
  static const String aboutRoute = '/about';
}
