import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/firestore_service.dart';

class AuthProvider extends ChangeNotifier {
  User? _firebaseUser;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  User? get firebaseUser => _firebaseUser;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _firebaseUser != null;
  bool get isAdmin => _userModel?.isAdmin ?? false;
  bool get isEmployee => _userModel?.isEmployee ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  // Initialize authentication state
  void _initializeAuth() {
    AuthService.authStateChanges.listen((User? user) async {
      _firebaseUser = user;
      if (user != null) {
        await _loadUserData();
      } else {
        _userModel = null;
      }
      notifyListeners();
    });
  }

  // Load user data from Firestore
  Future<void> _loadUserData() async {
    if (_firebaseUser == null) return;
    
    try {
      _userModel = await FirestoreService.getUser(_firebaseUser!.uid);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load user data: $e');
    }
  }

  // Sign in with email and password
  Future<bool> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final credential = await AuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential?.user != null) {
        _firebaseUser = credential!.user;
        await _loadUserData();
        _setLoading(false);
        return true;
      }
      
      _setLoading(false);
      return false;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Create user with email and password
  Future<bool> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String role = 'employee',
    String? department,
    String? phone,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final credential = await AuthService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
        role: role,
        department: department,
        phone: phone,
      );

      if (credential?.user != null) {
        _firebaseUser = credential!.user;
        await _loadUserData();
        _setLoading(false);
        return true;
      }
      
      _setLoading(false);
      return false;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.sendPasswordResetEmail(email);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? displayName,
    String? photoURL,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.updateProfile(
        displayName: displayName,
        photoURL: photoURL,
      );
      
      // Reload user data
      await _loadUserData();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Update user data in Firestore
  Future<bool> updateUserData(UserModel updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await FirestoreService.updateUser(updatedUser);
      _userModel = updatedUser;
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.signOut();
      _firebaseUser = null;
      _userModel = null;
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Delete account
  Future<bool> deleteAccount(String password) async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.deleteAccount(password);
      _firebaseUser = null;
      _userModel = null;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Send email verification
  Future<bool> sendEmailVerification() async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.sendEmailVerification();
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Reload user to get updated verification status
  Future<void> reloadUser() async {
    try {
      await AuthService.reloadUser();
      notifyListeners();
    } catch (e) {
      // Ignore reload errors
    }
  }

  // Check if email is verified
  bool get isEmailVerified => AuthService.isEmailVerified;

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Get user display name
  String get displayName {
    return _userModel?.displayName ?? 
           _firebaseUser?.displayName ?? 
           _firebaseUser?.email?.split('@').first ?? 
           'User';
  }

  // Get user email
  String get email => _firebaseUser?.email ?? '';

  // Get user ID
  String get userId => _firebaseUser?.uid ?? '';

  // Check if user has specific role
  bool hasRole(String role) {
    return _userModel?.role == role;
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (_firebaseUser != null) {
      await _loadUserData();
    }
  }
}
