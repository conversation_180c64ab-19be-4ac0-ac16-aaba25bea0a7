import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../utils/constants.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final authProvider = context.watch<AuthProvider>();
    final settingsProvider = context.watch<SettingsProvider>();
    final isArabic = settingsProvider.isArabic;

    return Drawer(
      child: Column(
        children: [
          // Header
          _buildDrawerHeader(context, authProvider, isArabic),
          
          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Home
                _buildDrawerItem(
                  context,
                  icon: Icons.home_outlined,
                  title: isArabic ? 'الرئيسية' : 'Home',
                  onTap: () {
                    Navigator.pop(context);
                    if (authProvider.isAdmin) {
                      context.go(AppConstants.adminDashboardRoute);
                    } else {
                      context.go(AppConstants.homeRoute);
                    }
                  },
                ),

                // Employee Menu Items
                if (!authProvider.isAdmin) ...[
                  _buildDrawerItem(
                    context,
                    icon: Icons.add_shopping_cart,
                    title: isArabic ? 'إضافة مبيعة' : 'Add Sale',
                    onTap: () {
                      Navigator.pop(context);
                      context.push('/home/<USER>');
                    },
                  ),
                  _buildDrawerItem(
                    context,
                    icon: Icons.list_alt,
                    title: isArabic ? 'مبيعاتي' : 'My Sales',
                    onTap: () {
                      Navigator.pop(context);
                      context.push('/home/<USER>');
                    },
                  ),
                ],

                // Admin Menu Items
                if (authProvider.isAdmin) ...[
                  _buildDrawerItem(
                    context,
                    icon: Icons.analytics,
                    title: isArabic ? 'لوحة التحكم' : 'Dashboard',
                    onTap: () {
                      Navigator.pop(context);
                      context.go(AppConstants.adminDashboardRoute);
                    },
                  ),
                  _buildDrawerItem(
                    context,
                    icon: Icons.shopping_cart,
                    title: isArabic ? 'جميع المبيعات' : 'All Sales',
                    onTap: () {
                      Navigator.pop(context);
                      context.push('/admin/all-sales');
                    },
                  ),
                  _buildDrawerItem(
                    context,
                    icon: Icons.people,
                    title: isArabic ? 'إدارة المستخدمين' : 'Manage Users',
                    onTap: () {
                      Navigator.pop(context);
                      context.push('/admin/manage-users');
                    },
                  ),
                  _buildDrawerItem(
                    context,
                    icon: Icons.assessment,
                    title: isArabic ? 'التقارير' : 'Reports',
                    onTap: () {
                      Navigator.pop(context);
                      context.push('/admin/reports');
                    },
                  ),
                ],

                const Divider(),

                // Settings
                _buildDrawerItem(
                  context,
                  icon: Icons.person,
                  title: isArabic ? 'الملف الشخصي' : 'Profile',
                  onTap: () {
                    Navigator.pop(context);
                    context.push(AppConstants.profileRoute);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings,
                  title: isArabic ? 'الإعدادات' : 'Settings',
                  onTap: () {
                    Navigator.pop(context);
                    context.push(AppConstants.settingsRoute);
                  },
                ),

                const Divider(),

                // Language Toggle
                _buildDrawerItem(
                  context,
                  icon: Icons.language,
                  title: isArabic ? 'English' : 'العربية',
                  onTap: () {
                    settingsProvider.toggleLanguage();
                  },
                ),

                // Theme Toggle
                _buildDrawerItem(
                  context,
                  icon: settingsProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                  title: isArabic 
                      ? (settingsProvider.isDarkMode ? 'الوضع الفاتح' : 'الوضع الداكن')
                      : (settingsProvider.isDarkMode ? 'Light Mode' : 'Dark Mode'),
                  onTap: () {
                    settingsProvider.toggleThemeMode();
                  },
                ),

                const Divider(),

                // About
                _buildDrawerItem(
                  context,
                  icon: Icons.info_outline,
                  title: isArabic ? 'حول التطبيق' : 'About',
                  onTap: () {
                    Navigator.pop(context);
                    context.push('/settings/about');
                  },
                ),
              ],
            ),
          ),

          // Sign Out
          Container(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showSignOutDialog(context, authProvider, isArabic),
                icon: const Icon(Icons.logout),
                label: Text(isArabic ? 'تسجيل الخروج' : 'Sign Out'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context, AuthProvider authProvider, bool isArabic) {
    return UserAccountsDrawerHeader(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
      ),
      currentAccountPicture: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.onPrimary,
        child: Text(
          authProvider.userModel?.initials ?? 'U',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      accountName: Text(
        authProvider.displayName,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
      accountEmail: Text(authProvider.email),
      otherAccountsPictures: [
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            authProvider.isAdmin ? Icons.admin_panel_settings : Icons.person,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 20,
          ),
        ),
      ],
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected 
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
      onTap: onTap,
    );
  }

  void _showSignOutDialog(BuildContext context, AuthProvider authProvider, bool isArabic) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'تسجيل الخروج' : 'Sign Out'),
          content: Text(
            isArabic 
                ? 'هل أنت متأكد من أنك تريد تسجيل الخروج؟'
                : 'Are you sure you want to sign out?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isArabic ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await authProvider.signOut();
                if (context.mounted) {
                  context.go(AppConstants.loginRoute);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: Text(isArabic ? 'تسجيل الخروج' : 'Sign Out'),
            ),
          ],
        );
      },
    );
  }
}
