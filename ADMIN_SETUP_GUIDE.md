# 🔐 دليل إعداد حساب المدير - تطبيق متتبع المبيعات

## الخطوة 1: إعداد مشروع Firebase

### أ. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اضغط على "Add project" أو "إضافة مشروع"
3. أدخل اسم المشروع: `sales-tracker-khalaifat`
4. اختر إعدادات Google Analytics (اختياري)
5. اضغط "Create project"

### ب. إضافة تطبيق الويب
1. في مشروع Firebase، اضغط على أيقونة الويب `</>`
2. أدخل اسم التطبيق: `Sales Tracker Khalaifat`
3. اضغط "Register app"
4. انسخ إعدادات Firebase التي ستظهر

### ج. تحديث إعدادات التطبيق
1. افتح ملف `lib/firebase_options.dart`
2. استبدل القيم في `FirebaseOptions web` بالقيم من Firebase Console

## الخطوة 2: تفعيل خدمات Firebase

### أ. تفعيل Authentication
1. اذهب إلى "Authentication" > "Sign-in method"
2. فعّل "Email/Password"
3. احفظ التغييرات

### ب. إنشاء Firestore Database
1. اذهب إلى "Firestore Database"
2. اضغط "Create database"
3. اختر "Start in test mode"
4. اختر المنطقة الجغرافية (مثل: europe-west3)

### ج. إعداد قواعد Firestore
انسخ والصق هذه القواعد في Firestore Rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Sales collection
    match /sales/{saleId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Products collection
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Reports collection
    match /reports/{reportId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

## الخطوة 3: إنشاء حساب المدير

### الطريقة الأولى: من خلال التطبيق
1. شغّل التطبيق: `flutter run -d chrome`
2. اذهب إلى صفحة "إنشاء حساب"
3. أدخل البيانات التالية:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `M1158m`
   - الاسم: `محمد جاسم`
   - القسم: `الإدارة`
4. اضغط "إنشاء الحساب"

### الطريقة الثانية: من Firebase Console
1. اذهب إلى "Authentication" > "Users"
2. اضغط "Add user"
3. أدخل البريد الإلكتروني وكلمة المرور
4. اضغط "Add user"

## الخطوة 4: تعيين دور المدير

### في Firestore Database:
1. اذهب إلى "Firestore Database"
2. ابحث عن مجموعة `users`
3. ابحث عن المستخدم بالبريد الإلكتروني `<EMAIL>`
4. اضغط على المستند
5. عدّل الحقول التالية:
   ```
   role: "admin"
   isAdmin: true
   isEmployee: false
   permissions: ["read_all", "write_all", "manage_users", "view_reports"]
   ```

## الخطوة 5: اختبار الحساب

### أ. اختبار تسجيل الدخول
1. اذهب إلى صفحة تسجيل الدخول
2. أدخل البيانات:
   - البريد الإلكتروني: `<EMAIL>`
   - كلمة المرور: `M1158m`
3. اضغط "تسجيل الدخول"

### ب. التحقق من الصلاحيات
- يجب أن يتم توجيهك إلى لوحة تحكم المدير
- تحقق من إمكانية الوصول إلى:
  - جميع المبيعات
  - إدارة المستخدمين
  - التقارير والإحصائيات
  - إعدادات النظام

## الخطوة 6: إنشاء مستخدمين إضافيين (اختياري)

يمكن للمدير إنشاء حسابات موظفين جديدة من خلال:
1. لوحة تحكم المدير
2. قسم "إدارة المستخدمين"
3. زر "إضافة موظف جديد"

## استكشاف الأخطاء

### مشكلة: لا يمكن تسجيل الدخول
- تأكد من تفعيل Email/Password في Firebase Authentication
- تحقق من صحة البريد الإلكتروني وكلمة المرور

### مشكلة: لا يظهر كمدير
- تأكد من تعديل حقل `role` إلى `"admin"` في Firestore
- تأكد من تعديل حقل `isAdmin` إلى `true`

### مشكلة: لا يمكن الوصول للبيانات
- تحقق من قواعد Firestore
- تأكد من أن المستخدم مصادق عليه

## معلومات الحساب النهائية

**بيانات المدير:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `M1158m`
- الدور: `admin`
- الصلاحيات: كاملة

**الوصول:**
- لوحة تحكم المدير: `/admin`
- إدارة المستخدمين: `/admin/manage-users`
- جميع المبيعات: `/admin/all-sales`
- التقارير: `/admin/reports`

## الأمان

⚠️ **تنبيه أمني:**
- غيّر كلمة المرور بعد أول تسجيل دخول
- استخدم كلمة مرور قوية
- فعّل المصادقة الثنائية إذا أمكن
- لا تشارك بيانات المدير مع أي شخص غير مخول
