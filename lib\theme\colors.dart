import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color primaryDark = Color(0xFF0D47A1);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF26A69A);
  static const Color secondaryLight = Color(0xFF4DB6AC);
  static const Color secondaryDark = Color(0xFF00695C);
  
  // Success Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF2E7D32);
  
  // Warning Colors
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFE65100);
  
  // Error Colors
  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);
  
  // Info Colors
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1565C0);
  
  // Neutral Colors - Light Theme
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color cardLight = Color(0xFFFFFFFF);
  static const Color dividerLight = Color(0xFFE0E0E0);
  
  // Text Colors - Light Theme
  static const Color textPrimaryLight = Color(0xFF212121);
  static const Color textSecondaryLight = Color(0xFF757575);
  static const Color textDisabledLight = Color(0xFFBDBDBD);
  static const Color textHintLight = Color(0xFF9E9E9E);
  
  // Neutral Colors - Dark Theme
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color cardDark = Color(0xFF2C2C2C);
  static const Color dividerDark = Color(0xFF373737);
  
  // Text Colors - Dark Theme
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
  static const Color textSecondaryDark = Color(0xFFB3B3B3);
  static const Color textDisabledDark = Color(0xFF666666);
  static const Color textHintDark = Color(0xFF999999);
  
  // Chart Colors
  static const List<Color> chartColors = [
    Color(0xFFFF6B6B), // Red
    Color(0xFF4ECDC4), // Teal
    Color(0xFF45B7D1), // Blue
    Color(0xFF96CEB4), // Green
    Color(0xFFFECA57), // Yellow
    Color(0xFFDDA0DD), // Plum
    Color(0xFF98D8C8), // Mint
    Color(0xFFF7DC6F), // Light Yellow
    Color(0xFFBB8FCE), // Light Purple
    Color(0xFF85C1E9), // Light Blue
  ];
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, warningLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, errorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowDark = Color(0x3A000000);
  
  // Overlay Colors
  static const Color overlayLight = Color(0x66000000);
  static const Color overlayDark = Color(0x80000000);
  
  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF373737);
  
  // Input Colors
  static const Color inputFillLight = Color(0xFFF5F5F5);
  static const Color inputFillDark = Color(0xFF2C2C2C);
  static const Color inputBorderLight = Color(0xFFE0E0E0);
  static const Color inputBorderDark = Color(0xFF373737);
  
  // Status Colors
  static const Color online = Color(0xFF4CAF50);
  static const Color offline = Color(0xFF9E9E9E);
  static const Color away = Color(0xFFFF9800);
  static const Color busy = Color(0xFFF44336);
  
  // Priority Colors
  static const Color priorityHigh = Color(0xFFF44336);
  static const Color priorityMedium = Color(0xFFFF9800);
  static const Color priorityLow = Color(0xFF4CAF50);
  
  // Category Colors
  static const Map<String, Color> categoryColors = {
    'electronics': Color(0xFF2196F3),
    'clothing': Color(0xFFE91E63),
    'food': Color(0xFF4CAF50),
    'books': Color(0xFF9C27B0),
    'sports': Color(0xFFFF5722),
    'home': Color(0xFF795548),
    'beauty': Color(0xFFFF4081),
    'automotive': Color(0xFF607D8B),
    'toys': Color(0xFFFFEB3B),
    'health': Color(0xFF009688),
  };
  
  // Role Colors
  static const Color adminRole = Color(0xFFF44336);
  static const Color managerRole = Color(0xFFFF9800);
  static const Color employeeRole = Color(0xFF4CAF50);
  static const Color guestRole = Color(0xFF9E9E9E);
  
  // Currency Colors
  static const Map<String, Color> currencyColors = {
    'BHD': Color(0xFFD32F2F), // Bahrain Red
    'SAR': Color(0xFF388E3C), // Saudi Green
    'USD': Color(0xFF1976D2), // US Blue
    'EUR': Color(0xFF7B1FA2), // Euro Purple
    'AED': Color(0xFFE64A19), // UAE Orange
    'KWD': Color(0xFF5D4037), // Kuwait Brown
    'QAR': Color(0xFF8E24AA), // Qatar Purple
    'OMR': Color(0xFF00796B), // Oman Teal
  };
  
  // Helper methods
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }
  
  static Color getCategoryColor(String category) {
    return categoryColors[category.toLowerCase()] ?? primary;
  }
  
  static Color getCurrencyColor(String currency) {
    return currencyColors[currency.toUpperCase()] ?? primary;
  }
  
  static Color getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return adminRole;
      case 'manager':
        return managerRole;
      case 'employee':
        return employeeRole;
      default:
        return guestRole;
    }
  }
  
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return priorityHigh;
      case 'medium':
        return priorityMedium;
      case 'low':
        return priorityLow;
      default:
        return priorityMedium;
    }
  }
}
