import 'package:flutter/material.dart';
import '../../utils/constants.dart';

enum ButtonType { primary, secondary, outline, text }
enum ButtonSize { small, medium, large }

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isFullWidth;
  final bool isLoading;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isFullWidth = false,
    this.isLoading = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.borderRadius,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Determine button style based on type
    ButtonStyle buttonStyle;
    Widget buttonChild;

    switch (type) {
      case ButtonType.primary:
        buttonStyle = ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? colorScheme.primary,
          foregroundColor: textColor ?? colorScheme.onPrimary,
          elevation: 2,
          shadowColor: colorScheme.shadow,
        );
        break;
      case ButtonType.secondary:
        buttonStyle = ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? colorScheme.secondary,
          foregroundColor: textColor ?? colorScheme.onSecondary,
          elevation: 1,
        );
        break;
      case ButtonType.outline:
        buttonStyle = OutlinedButton.styleFrom(
          foregroundColor: textColor ?? colorScheme.primary,
          side: BorderSide(
            color: backgroundColor ?? colorScheme.primary,
            width: 1.5,
          ),
        );
        break;
      case ButtonType.text:
        buttonStyle = TextButton.styleFrom(
          foregroundColor: textColor ?? colorScheme.primary,
        );
        break;
    }

    // Apply common style properties
    buttonStyle = buttonStyle.copyWith(
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            borderRadius ?? AppConstants.borderRadius,
          ),
        ),
      ),
      padding: MaterialStateProperty.all(
        padding ?? _getPaddingForSize(size),
      ),
      minimumSize: MaterialStateProperty.all(
        isFullWidth ? const Size(double.infinity, 0) : null,
      ),
    );

    // Create button content
    if (isLoading) {
      buttonChild = SizedBox(
        height: _getHeightForSize(size),
        child: Row(
          mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  type == ButtonType.outline || type == ButtonType.text
                      ? (textColor ?? colorScheme.primary)
                      : (textColor ?? colorScheme.onPrimary),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: _getTextStyleForSize(context, size),
            ),
          ],
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: _getIconSizeForSize(size),
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: _getTextStyleForSize(context, size),
          ),
        ],
      );
    } else {
      buttonChild = Text(
        text,
        style: _getTextStyleForSize(context, size),
      );
    }

    // Build the appropriate button widget
    switch (type) {
      case ButtonType.primary:
      case ButtonType.secondary:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case ButtonType.outline:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case ButtonType.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
    }
  }

  EdgeInsetsGeometry _getPaddingForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  double _getHeightForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return 32;
      case ButtonSize.medium:
        return 40;
      case ButtonSize.large:
        return 48;
    }
  }

  double _getIconSizeForSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
        return 20;
    }
  }

  TextStyle _getTextStyleForSize(BuildContext context, ButtonSize size) {
    final theme = Theme.of(context);
    switch (size) {
      case ButtonSize.small:
        return theme.textTheme.bodySmall?.copyWith(
          fontWeight: FontWeight.w500,
        ) ?? const TextStyle();
      case ButtonSize.medium:
        return theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ) ?? const TextStyle();
      case ButtonSize.large:
        return theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ) ?? const TextStyle();
    }
  }
}

// Convenience constructors
class PrimaryButton extends CustomButton {
  const PrimaryButton({
    super.key,
    required super.text,
    super.onPressed,
    super.size,
    super.isFullWidth,
    super.isLoading,
    super.icon,
  }) : super(type: ButtonType.primary);
}

class SecondaryButton extends CustomButton {
  const SecondaryButton({
    super.key,
    required super.text,
    super.onPressed,
    super.size,
    super.isFullWidth,
    super.isLoading,
    super.icon,
  }) : super(type: ButtonType.secondary);
}

class OutlineButton extends CustomButton {
  const OutlineButton({
    super.key,
    required super.text,
    super.onPressed,
    super.size,
    super.isFullWidth,
    super.isLoading,
    super.icon,
  }) : super(type: ButtonType.outline);
}

class TextOnlyButton extends CustomButton {
  const TextOnlyButton({
    super.key,
    required super.text,
    super.onPressed,
    super.size,
    super.isFullWidth,
    super.isLoading,
    super.icon,
  }) : super(type: ButtonType.text);
}
