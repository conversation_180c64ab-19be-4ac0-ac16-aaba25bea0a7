import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../screens/auth/splash_screen.dart';
import '../screens/auth/login_page.dart';
import '../screens/auth/register_page.dart';
import '../screens/auth/forgot_password_page.dart';
import '../screens/employee/home_page.dart';
import '../screens/admin/admin_dashboard.dart';
import '../screens/settings/profile_page.dart';
import '../screens/settings/settings_page.dart';
import '../utils/constants.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppConstants.splashRoute,
    redirect: _redirect,
    routes: [
      // Splash Screen
      GoRoute(
        path: AppConstants.splashRoute,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: AppConstants.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      
      GoRoute(
        path: AppConstants.registerRoute,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      
      GoRoute(
        path: AppConstants.forgotPasswordRoute,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      
      // Employee Routes
      GoRoute(
        path: AppConstants.homeRoute,
        name: 'home',
        builder: (context, state) => const HomePage(),
        routes: [
          GoRoute(
            path: 'add-sale',
            name: 'add-sale',
            builder: (context, state) => const AddSalePage(),
          ),
          GoRoute(
            path: 'my-sales',
            name: 'my-sales',
            builder: (context, state) => const MySalesPage(),
          ),
          GoRoute(
            path: 'sale/:id',
            name: 'sale-details',
            builder: (context, state) {
              final saleId = state.pathParameters['id']!;
              return SaleDetailsPage(saleId: saleId);
            },
          ),
          GoRoute(
            path: 'edit-sale/:id',
            name: 'edit-sale',
            builder: (context, state) {
              final saleId = state.pathParameters['id']!;
              return EditSalePage(saleId: saleId);
            },
          ),
        ],
      ),
      
      // Admin Routes
      GoRoute(
        path: AppConstants.adminDashboardRoute,
        name: 'admin-dashboard',
        builder: (context, state) => const AdminDashboard(),
        routes: [
          GoRoute(
            path: 'all-sales',
            name: 'all-sales',
            builder: (context, state) => const AllSalesPage(),
          ),
          GoRoute(
            path: 'manage-users',
            name: 'manage-users',
            builder: (context, state) => const ManageUsersPage(),
          ),
          GoRoute(
            path: 'add-user',
            name: 'add-user',
            builder: (context, state) => const AddUserPage(),
          ),
          GoRoute(
            path: 'reports',
            name: 'reports',
            builder: (context, state) => const ReportsPage(),
          ),
        ],
      ),
      
      // Settings Routes
      GoRoute(
        path: AppConstants.profileRoute,
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),
      
      GoRoute(
        path: AppConstants.settingsRoute,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
        routes: [
          GoRoute(
            path: 'change-password',
            name: 'change-password',
            builder: (context, state) => const ChangePasswordPage(),
          ),
          GoRoute(
            path: 'about',
            name: 'about',
            builder: (context, state) => const AboutPage(),
          ),
        ],
      ),
    ],
  );

  // Redirect logic
  static String? _redirect(BuildContext context, GoRouterState state) {
    final authProvider = context.read<AuthProvider>();
    final isAuthenticated = authProvider.isAuthenticated;
    final isAdmin = authProvider.isAdmin;
    final currentLocation = state.uri.path;

    // If on splash screen, stay there until auth state is determined
    if (currentLocation == AppConstants.splashRoute) {
      return null;
    }

    // If not authenticated and trying to access protected routes
    if (!isAuthenticated) {
      final publicRoutes = [
        AppConstants.loginRoute,
        AppConstants.registerRoute,
        AppConstants.forgotPasswordRoute,
      ];
      
      if (!publicRoutes.contains(currentLocation)) {
        return AppConstants.loginRoute;
      }
      return null;
    }

    // If authenticated and trying to access auth routes
    if (isAuthenticated) {
      final authRoutes = [
        AppConstants.loginRoute,
        AppConstants.registerRoute,
        AppConstants.forgotPasswordRoute,
      ];
      
      if (authRoutes.contains(currentLocation)) {
        return isAdmin ? AppConstants.adminDashboardRoute : AppConstants.homeRoute;
      }
    }

    // Admin route protection
    if (currentLocation.startsWith(AppConstants.adminDashboardRoute) && !isAdmin) {
      return AppConstants.homeRoute;
    }

    return null;
  }
}

// Placeholder pages - these will be implemented later
class AddSalePage extends StatelessWidget {
  const AddSalePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add Sale')),
      body: const Center(child: Text('Add Sale Page - Coming Soon')),
    );
  }
}

class MySalesPage extends StatelessWidget {
  const MySalesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('My Sales')),
      body: const Center(child: Text('My Sales Page - Coming Soon')),
    );
  }
}

class SaleDetailsPage extends StatelessWidget {
  final String saleId;
  
  const SaleDetailsPage({super.key, required this.saleId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sale Details')),
      body: Center(child: Text('Sale Details Page - Sale ID: $saleId')),
    );
  }
}

class EditSalePage extends StatelessWidget {
  final String saleId;
  
  const EditSalePage({super.key, required this.saleId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Sale')),
      body: Center(child: Text('Edit Sale Page - Sale ID: $saleId')),
    );
  }
}

class AllSalesPage extends StatelessWidget {
  const AllSalesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('All Sales')),
      body: const Center(child: Text('All Sales Page - Coming Soon')),
    );
  }
}

class ManageUsersPage extends StatelessWidget {
  const ManageUsersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Manage Users')),
      body: const Center(child: Text('Manage Users Page - Coming Soon')),
    );
  }
}

class AddUserPage extends StatelessWidget {
  const AddUserPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add User')),
      body: const Center(child: Text('Add User Page - Coming Soon')),
    );
  }
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reports')),
      body: const Center(child: Text('Reports Page - Coming Soon')),
    );
  }
}

class ChangePasswordPage extends StatelessWidget {
  const ChangePasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Change Password')),
      body: const Center(child: Text('Change Password Page - Coming Soon')),
    );
  }
}

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('About')),
      body: const Center(child: Text('About Page - Coming Soon')),
    );
  }
}
