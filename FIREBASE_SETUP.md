# 🔥 دليل ربط التطبيق مع Firebase

## الخطوة 1: إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اضغط على "Add project" أو "إضافة مشروع"
3. أدخل اسم المشروع: `sales-tracker`
4. اختر إعدادات Google Analytics (اختياري)
5. اضغط "Create project"

## الخطوة 2: إضافة تطبيق الويب

1. في مشروع Firebase، اضغط على أيقونة الويب `</>`
2. أدخل اسم التطبيق: `Sales Tracker Web`
3. اختر "Also set up Firebase Hosting" (اختياري)
4. اضغط "Register app"

## الخطوة 3: نسخ إعدادات Firebase

ستحصل على إعدادات مثل هذه:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "sales-tracker-xxxxx.firebaseapp.com",
  projectId: "sales-tracker-xxxxx",
  storageBucket: "sales-tracker-xxxxx.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

## الخطوة 4: تحديث ملف firebase_options.dart

افتح ملف `lib/firebase_options.dart` واستبدل القيم في `FirebaseOptions web`:

```dart
static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'YOUR_API_KEY_HERE', // من Firebase Console
  appId: 'YOUR_APP_ID_HERE', // من Firebase Console
  messagingSenderId: 'YOUR_MESSAGING_SENDER_ID_HERE', // من Firebase Console
  projectId: 'YOUR_PROJECT_ID_HERE', // من Firebase Console
  authDomain: 'YOUR_PROJECT_ID_HERE.firebaseapp.com',
  storageBucket: 'YOUR_PROJECT_ID_HERE.appspot.com',
  measurementId: 'YOUR_MEASUREMENT_ID_HERE', // اختياري
);
```

## الخطوة 5: تفعيل خدمات Firebase

### أ. Authentication (المصادقة)
1. اذهب إلى "Authentication" > "Sign-in method"
2. فعّل "Email/Password"
3. اختياري: فعّل "Google" أو خدمات أخرى

### ب. Firestore Database
1. اذهب إلى "Firestore Database"
2. اضغط "Create database"
3. اختر "Start in test mode" للتطوير
4. اختر المنطقة الجغرافية (مثل: europe-west3)

### ج. Storage (اختياري)
1. اذهب إلى "Storage"
2. اضغط "Get started"
3. اختر "Start in test mode"

## الخطوة 6: إعداد قواعد Firestore

في Firestore Database > Rules، استخدم هذه القواعد:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Sales collection
    match /sales/{saleId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Products collection
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Reports collection
    match /reports/{reportId} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

## الخطوة 7: اختبار الاتصال

1. احفظ جميع التغييرات
2. أعد تشغيل التطبيق:
   ```bash
   flutter run -d chrome
   ```
3. جرب إنشاء حساب جديد
4. تحقق من Firebase Console لرؤية المستخدم الجديد

## الخطوة 8: إنشاء مستخدم مدير

لإنشاء مستخدم مدير:

1. أنشئ حساب عادي في التطبيق
2. اذهب إلى Firestore Database في Firebase Console
3. ابحث عن مجموعة `users`
4. ابحث عن المستخدم الذي أنشأته
5. عدّل حقل `role` من `employee` إلى `admin`

## نصائح مهمة

### للتطوير:
- استخدم "Test mode" في Firestore
- فعّل Authentication بـ Email/Password

### للإنتاج:
- غيّر قواعد Firestore لتكون أكثر أماناً
- فعّل خدمات إضافية حسب الحاجة
- استخدم Firebase Hosting لنشر التطبيق

## استكشاف الأخطاء

### خطأ "FirebaseOptions cannot be null":
- تأكد من تحديث `firebase_options.dart` بالقيم الصحيحة
- تأكد من استيراد الملف في `main.dart`

### خطأ في المصادقة:
- تأكد من تفعيل Email/Password في Firebase Console
- تحقق من قواعد Firestore

### خطأ في قاعدة البيانات:
- تأكد من إنشاء Firestore Database
- تحقق من قواعد الأمان

## روابط مفيدة

- [Firebase Console](https://console.firebase.google.com/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [FlutterFire Documentation](https://firebase.flutter.dev/)
