// This is a basic Flutter widget test for Sales Tracker app with i18n support.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:sales_tracker/main.dart';
import 'package:sales_tracker/services/language_service.dart';

void main() {
  // Helper function to create app with localization support
  Widget createApp({Locale locale = const Locale('ar')}) {
    return MaterialApp(
      locale: locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LanguageService.supportedLocales,
      home: SalesTrackerHomePage(onLanguageChanged: (locale) {}),
    );
  }

  group('Sales Tracker App Tests', () {
    testWidgets('Sales Tracker app loads correctly in Arabic', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(createApp(locale: const Locale('ar')));
      await tester.pumpAndSettle();

      // Verify that the app title is displayed in Arabic.
      expect(find.text('متتبع المبيعات'), findsOneWidget);

      // Verify that the total sales section is displayed in Arabic.
      expect(find.text('إجمالي المبيعات'), findsOneWidget);
      expect(find.text('0.00 ريال'), findsOneWidget);

      // Verify that the empty state message is displayed in Arabic.
      expect(
        find.text('لا توجد مبيعات بعد\nاضغط على زر + لإضافة عملية بيع'),
        findsOneWidget,
      );

      // Verify that the floating action button is present.
      expect(find.byIcon(Icons.add), findsOneWidget);

      // Verify that the language toggle button is present.
      expect(find.byIcon(Icons.language), findsOneWidget);
    });

    testWidgets('Sales Tracker app loads correctly in English', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(createApp(locale: const Locale('en')));
      await tester.pumpAndSettle();

      // Verify that the app title is displayed in English.
      expect(find.text('Sales Tracker'), findsOneWidget);

      // Verify that the total sales section is displayed in English.
      expect(find.text('Total Sales'), findsOneWidget);
      expect(find.text('0.00 SAR'), findsOneWidget);

      // Verify that the empty state message is displayed in English.
      expect(
        find.text('No sales yet\nTap the + button to add a sale'),
        findsOneWidget,
      );

      // Verify that the floating action button is present.
      expect(find.byIcon(Icons.add), findsOneWidget);

      // Verify that the language toggle button is present.
      expect(find.byIcon(Icons.language), findsOneWidget);
    });

    testWidgets('Add sale dialog opens when FAB is tapped (Arabic)', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(createApp(locale: const Locale('ar')));
      await tester.pumpAndSettle();

      // Tap the floating action button.
      await tester.tap(find.byIcon(Icons.add));
      await tester.pump();

      // Verify that the dialog is displayed in Arabic.
      expect(find.text('إضافة عملية بيع جديدة'), findsOneWidget);
      expect(find.text('اسم المنتج'), findsOneWidget);
      expect(find.text('المبلغ'), findsOneWidget);
      expect(find.text('إلغاء'), findsOneWidget);
      expect(find.text('إضافة'), findsOneWidget);
    });

    testWidgets('Add sale dialog opens when FAB is tapped (English)', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(createApp(locale: const Locale('en')));
      await tester.pumpAndSettle();

      // Tap the floating action button.
      await tester.tap(find.byIcon(Icons.add));
      await tester.pump();

      // Verify that the dialog is displayed in English.
      expect(find.text('Add New Sale'), findsOneWidget);
      expect(find.text('Product Name'), findsOneWidget);
      expect(find.text('Amount'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Add'), findsOneWidget);
    });
  });
}
