// This is a basic Flutter widget test for Sales Tracker app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:sales_tracker/main.dart';

void main() {
  testWidgets('Sales Tracker app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SalesTrackerApp());

    // Verify that the app title is displayed.
    expect(find.text('متتبع المبيعات'), findsOneWidget);

    // Verify that the total sales section is displayed.
    expect(find.text('إجمالي المبيعات'), findsOneWidget);
    expect(find.text('0.00 ريال'), findsOneWidget);

    // Verify that the empty state message is displayed.
    expect(
      find.text('لا توجد مبيعات بعد\nاضغط على زر + لإضافة عملية بيع'),
      findsOneWidget,
    );

    // Verify that the floating action button is present.
    expect(find.byIcon(Icons.add), findsOneWidget);
  });

  testWidgets('Add sale dialog opens when FAB is tapped', (
    WidgetTester tester,
  ) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SalesTrackerApp());

    // Tap the floating action button.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that the dialog is displayed.
    expect(find.text('إضافة عملية بيع جديدة'), findsOneWidget);
    expect(find.text('اسم المنتج'), findsOneWidget);
    expect(find.text('المبلغ'), findsOneWidget);
    expect(find.text('إلغاء'), findsOneWidget);
    expect(find.text('إضافة'), findsOneWidget);
  });
}
