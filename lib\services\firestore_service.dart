import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/sale_model.dart';
import '../models/product_model.dart';
import '../models/report_model.dart';
import '../utils/constants.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== USER OPERATIONS ====================

  // Create user
  static Future<void> createUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .set(user.toMap());
    } catch (e) {
      throw Exception('Failed to create user: $e');
    }
  }

  // Get user by ID
  static Future<UserModel?> getUser(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();
      
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  // Update user
  static Future<void> updateUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .update(user.toMap());
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  // Get all users (admin only)
  static Future<List<UserModel>> getAllUsers() async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .orderBy('createdAt', descending: true)
          .get();
      
      return querySnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get users: $e');
    }
  }

  // Get users stream
  static Stream<List<UserModel>> getUsersStream() {
    return _firestore
        .collection(AppConstants.usersCollection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => UserModel.fromFirestore(doc))
            .toList());
  }

  // ==================== SALE OPERATIONS ====================

  // Create sale
  static Future<String> createSale(Sale sale) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.salesCollection)
          .add(sale.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create sale: $e');
    }
  }

  // Get sale by ID
  static Future<Sale?> getSale(String saleId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.salesCollection)
          .doc(saleId)
          .get();
      
      if (doc.exists) {
        return Sale.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get sale: $e');
    }
  }

  // Update sale
  static Future<void> updateSale(Sale sale) async {
    try {
      await _firestore
          .collection(AppConstants.salesCollection)
          .doc(sale.id)
          .update(sale.toMap());
    } catch (e) {
      throw Exception('Failed to update sale: $e');
    }
  }

  // Delete sale
  static Future<void> deleteSale(String saleId) async {
    try {
      await _firestore
          .collection(AppConstants.salesCollection)
          .doc(saleId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete sale: $e');
    }
  }

  // Get user sales
  static Future<List<Sale>> getUserSales(String userId, {int? limit}) async {
    try {
      Query query = _firestore
          .collection(AppConstants.salesCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('date', descending: true);
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Sale.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user sales: $e');
    }
  }

  // Get all sales (admin only)
  static Future<List<Sale>> getAllSales({int? limit}) async {
    try {
      Query query = _firestore
          .collection(AppConstants.salesCollection)
          .orderBy('date', descending: true);
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Sale.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get all sales: $e');
    }
  }

  // Get sales by date range
  static Future<List<Sale>> getSalesByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? userId,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.salesCollection)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date', descending: true);
      
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }
      
      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Sale.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get sales by date range: $e');
    }
  }

  // Get sales stream
  static Stream<List<Sale>> getSalesStream({String? userId, int? limit}) {
    Query query = _firestore
        .collection(AppConstants.salesCollection)
        .orderBy('date', descending: true);
    
    if (userId != null) {
      query = query.where('userId', isEqualTo: userId);
    }
    
    if (limit != null) {
      query = query.limit(limit);
    }
    
    return query.snapshots().map((snapshot) => 
        snapshot.docs.map((doc) => Sale.fromFirestore(doc)).toList());
  }

  // ==================== PRODUCT OPERATIONS ====================

  // Create product
  static Future<String> createProduct(Product product) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.productsCollection)
          .add(product.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  // Get product by ID
  static Future<Product?> getProduct(String productId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .get();
      
      if (doc.exists) {
        return Product.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get product: $e');
    }
  }

  // Update product
  static Future<void> updateProduct(Product product) async {
    try {
      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(product.id)
          .update(product.toMap());
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product
  static Future<void> deleteProduct(String productId) async {
    try {
      await _firestore
          .collection(AppConstants.productsCollection)
          .doc(productId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  // Get all products
  static Future<List<Product>> getAllProducts({bool activeOnly = true}) async {
    try {
      Query query = _firestore
          .collection(AppConstants.productsCollection)
          .orderBy('name');
      
      if (activeOnly) {
        query = query.where('isActive', isEqualTo: true);
      }
      
      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get products: $e');
    }
  }

  // Search products
  static Future<List<Product>> searchProducts(String searchTerm) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .where('isActive', isEqualTo: true)
          .get();
      
      final products = querySnapshot.docs
          .map((doc) => Product.fromFirestore(doc))
          .toList();
      
      // Filter products based on search term
      return products.where((product) => 
          product.matchesSearch(searchTerm)).toList();
    } catch (e) {
      throw Exception('Failed to search products: $e');
    }
  }

  // Get products stream
  static Stream<List<Product>> getProductsStream({bool activeOnly = true}) {
    Query query = _firestore
        .collection(AppConstants.productsCollection)
        .orderBy('name');
    
    if (activeOnly) {
      query = query.where('isActive', isEqualTo: true);
    }
    
    return query.snapshots().map((snapshot) => 
        snapshot.docs.map((doc) => Product.fromFirestore(doc)).toList());
  }

  // ==================== REPORT OPERATIONS ====================

  // Create report
  static Future<String> createReport(Report report) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.reportsCollection)
          .add(report.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create report: $e');
    }
  }

  // Get report by ID
  static Future<Report?> getReport(String reportId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.reportsCollection)
          .doc(reportId)
          .get();
      
      if (doc.exists) {
        return Report.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get report: $e');
    }
  }

  // Get all reports
  static Future<List<Report>> getAllReports({int? limit}) async {
    try {
      Query query = _firestore
          .collection(AppConstants.reportsCollection)
          .orderBy('generatedAt', descending: true);
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Report.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get reports: $e');
    }
  }

  // ==================== ANALYTICS OPERATIONS ====================

  // Get sales statistics
  static Future<Map<String, dynamic>> getSalesStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  }) async {
    try {
      Query query = _firestore.collection(AppConstants.salesCollection);
      
      if (startDate != null) {
        query = query.where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }
      
      if (endDate != null) {
        query = query.where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }
      
      if (userId != null) {
        query = query.where('userId', isEqualTo: userId);
      }
      
      final querySnapshot = await query.get();
      final sales = querySnapshot.docs
          .map((doc) => Sale.fromFirestore(doc))
          .toList();
      
      double totalSales = 0;
      int totalTransactions = sales.length;
      Map<String, double> salesByCategory = {};
      Map<String, int> salesByDay = {};
      
      for (final sale in sales) {
        totalSales += sale.totalPrice;
        
        // Group by category
        final category = sale.category ?? 'Other';
        salesByCategory[category] = (salesByCategory[category] ?? 0) + sale.totalPrice;
        
        // Group by day
        final dayKey = '${sale.date.year}-${sale.date.month}-${sale.date.day}';
        salesByDay[dayKey] = (salesByDay[dayKey] ?? 0) + 1;
      }
      
      return {
        'totalSales': totalSales,
        'totalTransactions': totalTransactions,
        'averageTransaction': totalTransactions > 0 ? totalSales / totalTransactions : 0,
        'salesByCategory': salesByCategory,
        'salesByDay': salesByDay,
      };
    } catch (e) {
      throw Exception('Failed to get sales statistics: $e');
    }
  }
}
