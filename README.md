# متتبع المبيعات (Sales Tracker)

تطبيق Flutter بسيط لتتبع المبيعات اليومية.

## الميزات

- ✅ إضافة عمليات بيع جديدة
- ✅ عرض إجمالي المبيعات
- ✅ قائمة بجميع المبيعات مع التواريخ
- ✅ دعم اللغتين العربية والإنجليزية (i18n)
- ✅ تبديل اللغة من داخل التطبيق
- ✅ دعم اتجاه النص (RTL/LTR)
- ✅ حفظ تفضيل اللغة
- ✅ تصميم Material Design 3

## كيفية التشغيل

1. تأكد من تثبيت Flutter على جهازك
2. افتح Terminal في مجلد المشروع
3. قم بتشغيل الأوامر التالية:

```bash
flutter pub get
flutter run
```

## كيفية الاستخدام

1. **إضافة عملية بيع جديدة**: اضغط على زر "+" في أسفل الشاشة
2. **ملء البيانات**: أدخل اسم المنتج والمبلغ
3. **حفظ العملية**: اضغط على زر "إضافة"
4. **عرض النتائج**: ستظهر العملية في القائمة ويتم تحديث إجمالي المبيعات
5. **تغيير اللغة**: اضغط على أيقونة اللغة في شريط التطبيق العلوي للتبديل بين العربية والإنجليزية

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Material Design 3**: للتصميم
- **Flutter Localizations**: للدعم متعدد اللغات
- **SharedPreferences**: لحفظ تفضيلات المستخدم
- **Firebase**: (جاهز للاستخدام - تم إضافة التبعيات)

## الاختبارات

لتشغيل الاختبارات:

```bash
flutter test
```

## الدعم متعدد اللغات (Internationalization)

يدعم التطبيق اللغتين العربية والإنجليزية مع الميزات التالية:

### اللغات المدعومة
- **العربية (ar)**: اللغة الافتراضية مع دعم RTL
- **الإنجليزية (en)**: مع دعم LTR

### كيفية تغيير اللغة
1. اضغط على أيقونة اللغة (🌐) في شريط التطبيق العلوي
2. سيتم التبديل تلقائياً بين العربية والإنجليزية
3. سيتم حفظ اختيارك تلقائياً للجلسات القادمة

### ملفات الترجمة
- `lib/l10n/app_ar.arb`: النصوص العربية
- `lib/l10n/app_en.arb`: النصوص الإنجليزية
- `lib/services/language_service.dart`: خدمة إدارة اللغات

### إضافة لغة جديدة
1. أنشئ ملف `.arb` جديد في مجلد `lib/l10n/`
2. أضف رمز اللغة إلى `supportedLocales` في `LanguageService`
3. قم بتشغيل `flutter gen-l10n` لإنتاج ملفات الترجمة

## المشاكل الشائعة وحلولها

### مشكلة "Flutter Demo" تظهر بدلاً من التطبيق
✅ **تم الحل**: تم استبدال كود Flutter Demo الافتراضي بتطبيق متتبع المبيعات

### مشاكل البناء على Android
إذا واجهت مشاكل في البناء، جرب:

```bash
flutter clean
flutter pub get
flutter run
```

## المطور

تم تطوير هذا التطبيق باستخدام Augment Agent
