# متتبع المبيعات (Sales Tracker)

تطبيق Flutter بسيط لتتبع المبيعات اليومية.

## الميزات

- ✅ إضافة عمليات بيع جديدة
- ✅ عرض إجمالي المبيعات
- ✅ قائمة بجميع المبيعات مع التواريخ
- ✅ واجهة مستخدم باللغة العربية
- ✅ تصميم Material Design 3

## كيفية التشغيل

1. تأكد من تثبيت Flutter على جهازك
2. افتح Terminal في مجلد المشروع
3. قم بتشغيل الأوامر التالية:

```bash
flutter pub get
flutter run
```

## كيفية الاستخدام

1. **إضافة عملية بيع جديدة**: اضغط على زر "+" في أسفل الشاشة
2. **ملء البيانات**: أدخل اسم المنتج والمبلغ
3. **حفظ العملية**: اضغط على زر "إضافة"
4. **عرض النتائج**: ستظهر العملية في القائمة ويتم تحديث إجمالي المبيعات

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Material Design 3**: للتصميم
- **Firebase**: (جاهز للاستخدام - تم إضافة التبعيات)

## الاختبارات

لتشغيل الاختبارات:

```bash
flutter test
```

## المشاكل الشائعة وحلولها

### مشكلة "Flutter Demo" تظهر بدلاً من التطبيق
✅ **تم الحل**: تم استبدال كود Flutter Demo الافتراضي بتطبيق متتبع المبيعات

### مشاكل البناء على Android
إذا واجهت مشاكل في البناء، جرب:

```bash
flutter clean
flutter pub get
flutter run
```

## المطور

تم تطوير هذا التطبيق باستخدام Augment Agent
