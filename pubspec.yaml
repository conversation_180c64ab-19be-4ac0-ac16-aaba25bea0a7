name: sales_tracker
description: "Daily Sales Tracker - Professional sales management system"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8

  # ✅ Internationalization and preferences
  intl: any
  shared_preferences: ^2.2.2

  # ✅ Firebase dependencies
  firebase_core: ^2.30.0
  firebase_auth: ^4.17.8
  cloud_firestore: ^4.15.8
  firebase_storage: ^11.6.9

  # ✅ State Management
  provider: ^6.1.1

  # ✅ Navigation
  go_router: ^13.2.0

  # ✅ UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  image_picker: ^1.0.7

  # ✅ Charts and Analytics
  fl_chart: ^0.66.2

  # ✅ Date and Time
  table_calendar: ^3.0.9

  # ✅ File Handling and Export
  pdf: ^3.10.7
  csv: ^5.1.1
  path_provider: ^2.1.2
  file_picker: ^6.1.1

  # ✅ Utilities
  uuid: ^4.3.3
  connectivity_plus: ^5.0.2
  permission_handler: ^11.3.0

  # ✅ Animations
  lottie: ^3.0.0

  # ✅ Local Database (for offline support)
  sqflite: ^2.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # ✅ Internationalization configuration
  generate: true
