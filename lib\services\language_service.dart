import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageService {
  static const String _languageKey = 'selected_language';
  
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('ar'), // Arabic
    Locale('en'), // English
  ];
  
  // Default locale (Arabic)
  static const Locale defaultLocale = Locale('ar');
  
  /// Get the saved language preference
  static Future<Locale> getSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey);
    
    if (languageCode != null) {
      return Locale(languageCode);
    }
    
    return defaultLocale;
  }
  
  /// Save the language preference
  static Future<void> saveLanguage(Locale locale) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, locale.languageCode);
  }
  
  /// Check if a locale is supported
  static bool isSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) => 
        supportedLocale.languageCode == locale.languageCode);
  }
  
  /// Get the opposite language (for language switching)
  static Locale getOppositeLanguage(Locale currentLocale) {
    if (currentLocale.languageCode == 'ar') {
      return const Locale('en');
    } else {
      return const Locale('ar');
    }
  }
}
