import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../services/currency_service.dart';
import '../../widgets/currency_picker.dart';
import '../admin/admin_setup_page.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final settingsProvider = context.watch<SettingsProvider>();
    final authProvider = context.watch<AuthProvider>();
    final isArabic = settingsProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'الإعدادات' : 'Settings'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Account Section
          _buildSectionHeader(context, isArabic ? 'الحساب' : 'Account'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.person),
                  title: Text(isArabic ? 'الملف الشخصي' : 'Profile'),
                  subtitle: Text(authProvider.displayName),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    context.push('/profile');
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.lock),
                  title: Text(
                    isArabic ? 'تغيير كلمة المرور' : 'Change Password',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    context.push('/settings/change-password');
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Appearance Section
          _buildSectionHeader(context, isArabic ? 'المظهر' : 'Appearance'),
          Card(
            child: Column(
              children: [
                // Language Setting
                ListTile(
                  leading: const Icon(Icons.language),
                  title: Text(isArabic ? 'اللغة' : 'Language'),
                  subtitle: Text(
                    settingsProvider.getLanguageName(settingsProvider.locale),
                  ),
                  trailing: Switch(
                    value: isArabic,
                    onChanged: (value) {
                      settingsProvider.toggleLanguage();
                    },
                  ),
                ),
                const Divider(height: 1),

                // Theme Setting
                ListTile(
                  leading: Icon(
                    settingsProvider.isDarkMode
                        ? Icons.dark_mode
                        : Icons.light_mode,
                  ),
                  title: Text(isArabic ? 'المظهر' : 'Theme'),
                  subtitle: Text(settingsProvider.themeModeName),
                  trailing: PopupMenuButton<ThemeMode>(
                    onSelected: (ThemeMode mode) {
                      settingsProvider.changeThemeMode(mode);
                    },
                    itemBuilder:
                        (BuildContext context) => [
                          PopupMenuItem(
                            value: ThemeMode.system,
                            child: Row(
                              children: [
                                const Icon(Icons.brightness_auto),
                                const SizedBox(width: 12),
                                Text(isArabic ? 'النظام' : 'System'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: ThemeMode.light,
                            child: Row(
                              children: [
                                const Icon(Icons.light_mode),
                                const SizedBox(width: 12),
                                Text(isArabic ? 'فاتح' : 'Light'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: ThemeMode.dark,
                            child: Row(
                              children: [
                                const Icon(Icons.dark_mode),
                                const SizedBox(width: 12),
                                Text(isArabic ? 'داكن' : 'Dark'),
                              ],
                            ),
                          ),
                        ],
                    child: const Icon(Icons.arrow_drop_down),
                  ),
                ),
                const Divider(height: 1),

                // Currency Setting
                ListTile(
                  leading: const Icon(Icons.attach_money),
                  title: Text(isArabic ? 'العملة' : 'Currency'),
                  subtitle: Text(settingsProvider.currencyName),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showCurrencyPicker(context, settingsProvider, isArabic);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Notifications Section
          _buildSectionHeader(
            context,
            isArabic ? 'الإشعارات' : 'Notifications',
          ),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.notifications),
                  title: Text(
                    isArabic ? 'إشعارات المبيعات' : 'Sales Notifications',
                  ),
                  subtitle: Text(
                    isArabic
                        ? 'تلقي إشعارات عند إضافة مبيعات جديدة'
                        : 'Receive notifications for new sales',
                  ),
                  trailing: Switch(
                    value: true, // This would be from settings
                    onChanged: (value) {
                      // Handle notification setting
                      _showComingSoonSnackBar(context);
                    },
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.email),
                  title: Text(
                    isArabic
                        ? 'إشعارات البريد الإلكتروني'
                        : 'Email Notifications',
                  ),
                  subtitle: Text(
                    isArabic
                        ? 'تلقي تقارير دورية عبر البريد الإلكتروني'
                        : 'Receive periodic reports via email',
                  ),
                  trailing: Switch(
                    value: false, // This would be from settings
                    onChanged: (value) {
                      // Handle email notification setting
                      _showComingSoonSnackBar(context);
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Data Section
          _buildSectionHeader(context, isArabic ? 'البيانات' : 'Data'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.backup),
                  title: Text(isArabic ? 'نسخ احتياطي' : 'Backup Data'),
                  subtitle: Text(
                    isArabic
                        ? 'إنشاء نسخة احتياطية من بياناتك'
                        : 'Create a backup of your data',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showComingSoonSnackBar(context);
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.download),
                  title: Text(isArabic ? 'تصدير البيانات' : 'Export Data'),
                  subtitle: Text(
                    isArabic
                        ? 'تصدير بياناتك بصيغة CSV أو PDF'
                        : 'Export your data as CSV or PDF',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showComingSoonSnackBar(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Admin Section (only for admins)
          if (authProvider.isAdmin) ...[
            const SizedBox(height: 24),
            _buildSectionHeader(
              context,
              isArabic ? 'إعدادات المدير' : 'Admin Settings',
            ),
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.admin_panel_settings),
                    title: Text(
                      isArabic
                          ? 'إعداد حساب مدير جديد'
                          : 'Setup New Admin Account',
                    ),
                    subtitle: Text(
                      isArabic
                          ? 'إنشاء حساب مدير إضافي'
                          : 'Create additional admin account',
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AdminSetupPage(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 24),

          // About Section
          _buildSectionHeader(context, isArabic ? 'حول التطبيق' : 'About'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.info),
                  title: Text(isArabic ? 'حول التطبيق' : 'About App'),
                  subtitle: Text(
                    isArabic
                        ? 'معلومات التطبيق والإصدار'
                        : 'App information and version',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    context.push('/settings/about');
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.help),
                  title: Text(isArabic ? 'المساعدة والدعم' : 'Help & Support'),
                  subtitle: Text(
                    isArabic
                        ? 'الحصول على المساعدة والدعم الفني'
                        : 'Get help and technical support',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showComingSoonSnackBar(context);
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: Text(isArabic ? 'سياسة الخصوصية' : 'Privacy Policy'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    _showComingSoonSnackBar(context);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Danger Zone
          _buildSectionHeader(
            context,
            isArabic ? 'منطقة الخطر' : 'Danger Zone',
          ),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: Icon(
                    Icons.logout,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  title: Text(
                    isArabic ? 'تسجيل الخروج' : 'Sign Out',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  onTap: () {
                    _showSignOutDialog(context, authProvider, isArabic);
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: Icon(
                    Icons.delete_forever,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  title: Text(
                    isArabic ? 'حذف الحساب' : 'Delete Account',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  onTap: () {
                    _showDeleteAccountDialog(context, authProvider, isArabic);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  void _showCurrencyPicker(
    BuildContext context,
    SettingsProvider settingsProvider,
    bool isArabic,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return CurrencySelectionBottomSheet(
          selectedCurrency: settingsProvider.currency,
          onCurrencySelected: (currency) {
            settingsProvider.changeCurrency(currency);
            Navigator.pop(context);
          },
        );
      },
    );
  }

  void _showSignOutDialog(
    BuildContext context,
    AuthProvider authProvider,
    bool isArabic,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'تسجيل الخروج' : 'Sign Out'),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من أنك تريد تسجيل الخروج؟'
                : 'Are you sure you want to sign out?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isArabic ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await authProvider.signOut();
                if (context.mounted) {
                  context.go('/login');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: Text(isArabic ? 'تسجيل الخروج' : 'Sign Out'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteAccountDialog(
    BuildContext context,
    AuthProvider authProvider,
    bool isArabic,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'حذف الحساب' : 'Delete Account'),
          content: Text(
            isArabic
                ? 'تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.'
                : 'Warning: This action cannot be undone. All your data will be permanently deleted.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isArabic ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showComingSoonSnackBar(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: Text(isArabic ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    );
  }

  void _showComingSoonSnackBar(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Feature coming soon!')));
  }
}
