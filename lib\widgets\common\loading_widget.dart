import 'package:flutter/material.dart';
import '../../utils/constants.dart';

enum LoadingType { circular, linear, dots, pulse }
enum LoadingSize { small, medium, large }

class LoadingWidget extends StatelessWidget {
  final LoadingType type;
  final LoadingSize size;
  final Color? color;
  final String? message;
  final bool showMessage;
  final double? strokeWidth;

  const LoadingWidget({
    super.key,
    this.type = LoadingType.circular,
    this.size = LoadingSize.medium,
    this.color,
    this.message,
    this.showMessage = false,
    this.strokeWidth,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final loadingColor = color ?? colorScheme.primary;

    Widget loadingIndicator;

    switch (type) {
      case LoadingType.circular:
        loadingIndicator = SizedBox(
          width: _getSizeValue(),
          height: _getSizeValue(),
          child: CircularProgressIndicator(
            color: loadingColor,
            strokeWidth: strokeWidth ?? _getStrokeWidth(),
          ),
        );
        break;
      case LoadingType.linear:
        loadingIndicator = SizedBox(
          width: _getSizeValue() * 2,
          child: LinearProgressIndicator(
            color: loadingColor,
            backgroundColor: loadingColor.withOpacity(0.2),
          ),
        );
        break;
      case LoadingType.dots:
        loadingIndicator = DotsLoadingIndicator(
          color: loadingColor,
          size: _getSizeValue() / 4,
        );
        break;
      case LoadingType.pulse:
        loadingIndicator = PulseLoadingIndicator(
          color: loadingColor,
          size: _getSizeValue(),
        );
        break;
    }

    if (showMessage && message != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          loadingIndicator,
          const SizedBox(height: 16),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return loadingIndicator;
  }

  double _getSizeValue() {
    switch (size) {
      case LoadingSize.small:
        return 20;
      case LoadingSize.medium:
        return 40;
      case LoadingSize.large:
        return 60;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case LoadingSize.small:
        return 2;
      case LoadingSize.medium:
        return 3;
      case LoadingSize.large:
        return 4;
    }
  }
}

// Dots Loading Indicator
class DotsLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;

  const DotsLoadingIndicator({
    super.key,
    required this.color,
    required this.size,
  });

  @override
  State<DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: EdgeInsets.symmetric(horizontal: widget.size / 4),
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.3 + (_animations[index].value * 0.7)),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

// Pulse Loading Indicator
class PulseLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;

  const PulseLoadingIndicator({
    super.key,
    required this.color,
    required this.size,
  });

  @override
  State<PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: widget.color.withOpacity(0.3 + (_animation.value * 0.7)),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }
}

// Convenience constructors
class SmallLoadingWidget extends LoadingWidget {
  const SmallLoadingWidget({
    super.key,
    super.type,
    super.color,
  }) : super(size: LoadingSize.small);
}

class LargeLoadingWidget extends LoadingWidget {
  const LargeLoadingWidget({
    super.key,
    super.type,
    super.color,
    super.message,
    super.showMessage,
  }) : super(size: LoadingSize.large);
}

class FullScreenLoadingWidget extends StatelessWidget {
  final String? message;
  final Color? backgroundColor;
  final Color? indicatorColor;

  const FullScreenLoadingWidget({
    super.key,
    this.message,
    this.backgroundColor,
    this.indicatorColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      color: backgroundColor ?? colorScheme.surface.withOpacity(0.8),
      child: Center(
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: LoadingWidget(
              type: LoadingType.circular,
              size: LoadingSize.large,
              color: indicatorColor,
              message: message,
              showMessage: message != null,
            ),
          ),
        ),
      ),
    );
  }
}

class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? loadingMessage;

  const LoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          FullScreenLoadingWidget(
            message: loadingMessage,
          ),
      ],
    );
  }
}
