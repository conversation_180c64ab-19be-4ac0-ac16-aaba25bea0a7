import 'package:cloud_firestore/cloud_firestore.dart';

class Report {
  final String id;
  final String title;
  final ReportType type;
  final ReportPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final double totalSales;
  final int totalTransactions;
  final String? topEmployeeId;
  final String? topEmployeeName;
  final Map<String, double> salesByCategory;
  final Map<String, double> salesByEmployee;
  final Map<String, int> transactionsByDay;
  final String currency;
  final String generatedBy;
  final DateTime generatedAt;
  final Map<String, dynamic>? metadata;

  Report({
    required this.id,
    required this.title,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.totalSales,
    required this.totalTransactions,
    this.topEmployeeId,
    this.topEmployeeName,
    required this.salesByCategory,
    required this.salesByEmployee,
    required this.transactionsByDay,
    required this.currency,
    required this.generatedBy,
    required this.generatedAt,
    this.metadata,
  });

  // Factory constructor from Firestore document
  factory Report.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Report(
      id: doc.id,
      title: data['title'] ?? '',
      type: ReportType.fromString(data['type'] ?? 'sales'),
      period: ReportPeriod.fromString(data['period'] ?? 'daily'),
      startDate: (data['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (data['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalSales: (data['totalSales'] ?? 0.0).toDouble(),
      totalTransactions: data['totalTransactions'] ?? 0,
      topEmployeeId: data['topEmployeeId'],
      topEmployeeName: data['topEmployeeName'],
      salesByCategory: Map<String, double>.from(data['salesByCategory'] ?? {}),
      salesByEmployee: Map<String, double>.from(data['salesByEmployee'] ?? {}),
      transactionsByDay: Map<String, int>.from(data['transactionsByDay'] ?? {}),
      currency: data['currency'] ?? 'BHD',
      generatedBy: data['generatedBy'] ?? '',
      generatedAt: (data['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      metadata: data['metadata'] as Map<String, dynamic>?,
    );
  }

  // Factory constructor from Map
  factory Report.fromMap(Map<String, dynamic> map, String id) {
    return Report(
      id: id,
      title: map['title'] ?? '',
      type: ReportType.fromString(map['type'] ?? 'sales'),
      period: ReportPeriod.fromString(map['period'] ?? 'daily'),
      startDate: (map['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (map['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      totalSales: (map['totalSales'] ?? 0.0).toDouble(),
      totalTransactions: map['totalTransactions'] ?? 0,
      topEmployeeId: map['topEmployeeId'],
      topEmployeeName: map['topEmployeeName'],
      salesByCategory: Map<String, double>.from(map['salesByCategory'] ?? {}),
      salesByEmployee: Map<String, double>.from(map['salesByEmployee'] ?? {}),
      transactionsByDay: Map<String, int>.from(map['transactionsByDay'] ?? {}),
      currency: map['currency'] ?? 'BHD',
      generatedBy: map['generatedBy'] ?? '',
      generatedAt: (map['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'type': type.value,
      'period': period.value,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'totalSales': totalSales,
      'totalTransactions': totalTransactions,
      'topEmployeeId': topEmployeeId,
      'topEmployeeName': topEmployeeName,
      'salesByCategory': salesByCategory,
      'salesByEmployee': salesByEmployee,
      'transactionsByDay': transactionsByDay,
      'currency': currency,
      'generatedBy': generatedBy,
      'generatedAt': Timestamp.fromDate(generatedAt),
      'metadata': metadata,
    };
  }

  // Copy with method
  Report copyWith({
    String? id,
    String? title,
    ReportType? type,
    ReportPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    double? totalSales,
    int? totalTransactions,
    String? topEmployeeId,
    String? topEmployeeName,
    Map<String, double>? salesByCategory,
    Map<String, double>? salesByEmployee,
    Map<String, int>? transactionsByDay,
    String? currency,
    String? generatedBy,
    DateTime? generatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Report(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      totalSales: totalSales ?? this.totalSales,
      totalTransactions: totalTransactions ?? this.totalTransactions,
      topEmployeeId: topEmployeeId ?? this.topEmployeeId,
      topEmployeeName: topEmployeeName ?? this.topEmployeeName,
      salesByCategory: salesByCategory ?? this.salesByCategory,
      salesByEmployee: salesByEmployee ?? this.salesByEmployee,
      transactionsByDay: transactionsByDay ?? this.transactionsByDay,
      currency: currency ?? this.currency,
      generatedBy: generatedBy ?? this.generatedBy,
      generatedAt: generatedAt ?? this.generatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  double get averageSalePerTransaction {
    return totalTransactions > 0 ? totalSales / totalTransactions : 0.0;
  }

  String get topCategory {
    if (salesByCategory.isEmpty) return 'N/A';
    return salesByCategory.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  double get topCategorySales {
    if (salesByCategory.isEmpty) return 0.0;
    return salesByCategory.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .value;
  }

  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }

  double get averageDailySales {
    return durationInDays > 0 ? totalSales / durationInDays : 0.0;
  }

  // Get metadata value
  T? getMetadata<T>(String key, [T? defaultValue]) {
    if (metadata == null) return defaultValue;
    return metadata![key] as T? ?? defaultValue;
  }

  // Set metadata value
  Report setMetadata(String key, dynamic value) {
    final newMetadata = Map<String, dynamic>.from(metadata ?? {});
    newMetadata[key] = value;
    return copyWith(metadata: newMetadata);
  }

  @override
  String toString() {
    return 'Report(id: $id, title: $title, type: ${type.value}, period: ${period.value}, totalSales: $totalSales)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Report && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Report type enum
enum ReportType {
  sales('sales'),
  employee('employee'),
  product('product'),
  customer('customer'),
  financial('financial');

  const ReportType(this.value);
  final String value;

  static ReportType fromString(String type) {
    for (final reportType in ReportType.values) {
      if (reportType.value == type.toLowerCase()) {
        return reportType;
      }
    }
    return ReportType.sales;
  }

  String get displayName {
    switch (this) {
      case ReportType.sales:
        return 'Sales Report';
      case ReportType.employee:
        return 'Employee Report';
      case ReportType.product:
        return 'Product Report';
      case ReportType.customer:
        return 'Customer Report';
      case ReportType.financial:
        return 'Financial Report';
    }
  }
}

// Report period enum
enum ReportPeriod {
  daily('daily'),
  weekly('weekly'),
  monthly('monthly'),
  quarterly('quarterly'),
  yearly('yearly'),
  custom('custom');

  const ReportPeriod(this.value);
  final String value;

  static ReportPeriod fromString(String period) {
    for (final reportPeriod in ReportPeriod.values) {
      if (reportPeriod.value == period.toLowerCase()) {
        return reportPeriod;
      }
    }
    return ReportPeriod.daily;
  }

  String get displayName {
    switch (this) {
      case ReportPeriod.daily:
        return 'Daily';
      case ReportPeriod.weekly:
        return 'Weekly';
      case ReportPeriod.monthly:
        return 'Monthly';
      case ReportPeriod.quarterly:
        return 'Quarterly';
      case ReportPeriod.yearly:
        return 'Yearly';
      case ReportPeriod.custom:
        return 'Custom';
    }
  }
}

// Report summary class for quick overview
class ReportSummary {
  final double totalSales;
  final int totalTransactions;
  final double averageTransaction;
  final String topEmployee;
  final String topCategory;
  final DateTime lastUpdated;

  ReportSummary({
    required this.totalSales,
    required this.totalTransactions,
    required this.averageTransaction,
    required this.topEmployee,
    required this.topCategory,
    required this.lastUpdated,
  });

  factory ReportSummary.fromReport(Report report) {
    return ReportSummary(
      totalSales: report.totalSales,
      totalTransactions: report.totalTransactions,
      averageTransaction: report.averageSalePerTransaction,
      topEmployee: report.topEmployeeName ?? 'N/A',
      topCategory: report.topCategory,
      lastUpdated: report.generatedAt,
    );
  }
}
