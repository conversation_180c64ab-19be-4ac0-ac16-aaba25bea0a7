import 'package:cloud_firestore/cloud_firestore.dart';

class Product {
  final String id;
  final String name;
  final String? description;
  final String category;
  final double defaultPrice;
  final String? imageUrl;
  final String? barcode;
  final int? stockQuantity;
  final int? minStockLevel;
  final bool isActive;
  final String? unit; // e.g., 'piece', 'kg', 'liter'
  final Map<String, dynamic>? attributes; // Custom attributes
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String createdBy;

  Product({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    required this.defaultPrice,
    this.imageUrl,
    this.barcode,
    this.stockQuantity,
    this.minStockLevel,
    this.isActive = true,
    this.unit,
    this.attributes,
    required this.createdAt,
    this.updatedAt,
    required this.createdBy,
  });

  // Factory constructor from Firestore document
  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Product(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      category: data['category'] ?? 'General',
      defaultPrice: (data['defaultPrice'] ?? 0.0).toDouble(),
      imageUrl: data['imageUrl'],
      barcode: data['barcode'],
      stockQuantity: data['stockQuantity'],
      minStockLevel: data['minStockLevel'],
      isActive: data['isActive'] ?? true,
      unit: data['unit'],
      attributes: data['attributes'] as Map<String, dynamic>?,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      createdBy: data['createdBy'] ?? '',
    );
  }

  // Factory constructor from Map
  factory Product.fromMap(Map<String, dynamic> map, String id) {
    return Product(
      id: id,
      name: map['name'] ?? '',
      description: map['description'],
      category: map['category'] ?? 'General',
      defaultPrice: (map['defaultPrice'] ?? 0.0).toDouble(),
      imageUrl: map['imageUrl'],
      barcode: map['barcode'],
      stockQuantity: map['stockQuantity'],
      minStockLevel: map['minStockLevel'],
      isActive: map['isActive'] ?? true,
      unit: map['unit'],
      attributes: map['attributes'] as Map<String, dynamic>?,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      createdBy: map['createdBy'] ?? '',
    );
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'category': category,
      'defaultPrice': defaultPrice,
      'imageUrl': imageUrl,
      'barcode': barcode,
      'stockQuantity': stockQuantity,
      'minStockLevel': minStockLevel,
      'isActive': isActive,
      'unit': unit,
      'attributes': attributes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdBy': createdBy,
    };
  }

  // Copy with method
  Product copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    double? defaultPrice,
    String? imageUrl,
    String? barcode,
    int? stockQuantity,
    int? minStockLevel,
    bool? isActive,
    String? unit,
    Map<String, dynamic>? attributes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      defaultPrice: defaultPrice ?? this.defaultPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      barcode: barcode ?? this.barcode,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      isActive: isActive ?? this.isActive,
      unit: unit ?? this.unit,
      attributes: attributes ?? this.attributes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  // Helper methods
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasBarcode => barcode != null && barcode!.isNotEmpty;
  bool get hasStock => stockQuantity != null;
  bool get isLowStock => hasStock && minStockLevel != null && stockQuantity! <= minStockLevel!;
  bool get isOutOfStock => hasStock && stockQuantity! <= 0;
  bool get isUpdated => updatedAt != null;

  String get displayUnit => unit ?? 'piece';
  String get displayDescription => description ?? 'No description available';

  // Stock management
  Product updateStock(int newQuantity) {
    return copyWith(
      stockQuantity: newQuantity,
      updatedAt: DateTime.now(),
    );
  }

  Product addStock(int quantity) {
    final currentStock = stockQuantity ?? 0;
    return updateStock(currentStock + quantity);
  }

  Product removeStock(int quantity) {
    final currentStock = stockQuantity ?? 0;
    final newStock = (currentStock - quantity).clamp(0, double.infinity).toInt();
    return updateStock(newStock);
  }

  // Price management
  Product updatePrice(double newPrice) {
    return copyWith(
      defaultPrice: newPrice,
      updatedAt: DateTime.now(),
    );
  }

  // Activate/Deactivate product
  Product activate() => copyWith(isActive: true, updatedAt: DateTime.now());
  Product deactivate() => copyWith(isActive: false, updatedAt: DateTime.now());

  // Mark as updated
  Product markAsUpdated() {
    return copyWith(updatedAt: DateTime.now());
  }

  // Get attribute value
  T? getAttribute<T>(String key, [T? defaultValue]) {
    if (attributes == null) return defaultValue;
    return attributes![key] as T? ?? defaultValue;
  }

  // Set attribute value
  Product setAttribute(String key, dynamic value) {
    final newAttributes = Map<String, dynamic>.from(attributes ?? {});
    newAttributes[key] = value;
    return copyWith(
      attributes: newAttributes,
      updatedAt: DateTime.now(),
    );
  }

  // Remove attribute
  Product removeAttribute(String key) {
    if (attributes == null) return this;
    final newAttributes = Map<String, dynamic>.from(attributes!);
    newAttributes.remove(key);
    return copyWith(
      attributes: newAttributes,
      updatedAt: DateTime.now(),
    );
  }

  // Search helper
  bool matchesSearch(String query) {
    final lowerQuery = query.toLowerCase();
    return name.toLowerCase().contains(lowerQuery) ||
           (description?.toLowerCase().contains(lowerQuery) ?? false) ||
           category.toLowerCase().contains(lowerQuery) ||
           (barcode?.toLowerCase().contains(lowerQuery) ?? false);
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, category: $category, price: $defaultPrice, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Product category enum
enum ProductCategory {
  electronics('Electronics'),
  clothing('Clothing'),
  food('Food & Beverages'),
  books('Books'),
  sports('Sports'),
  home('Home & Garden'),
  beauty('Beauty & Health'),
  automotive('Automotive'),
  toys('Toys & Games'),
  other('Other');

  const ProductCategory(this.displayName);
  final String displayName;

  static ProductCategory fromString(String category) {
    for (final cat in ProductCategory.values) {
      if (cat.displayName.toLowerCase() == category.toLowerCase()) {
        return cat;
      }
    }
    return ProductCategory.other;
  }
}

// Stock status enum
enum StockStatus {
  inStock,
  lowStock,
  outOfStock;

  bool get isInStock => this == StockStatus.inStock;
  bool get isLowStock => this == StockStatus.lowStock;
  bool get isOutOfStock => this == StockStatus.outOfStock;

  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'In Stock';
      case StockStatus.lowStock:
        return 'Low Stock';
      case StockStatus.outOfStock:
        return 'Out of Stock';
    }
  }
}
