// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    // 🔥 استبدل هذه القيم بالقيم من Firebase Console
    apiKey: 'YOUR_API_KEY_HERE', // من Firebase Console
    appId: 'YOUR_APP_ID_HERE', // من Firebase Console
    messagingSenderId: 'YOUR_MESSAGING_SENDER_ID_HERE', // من Firebase Console
    projectId: 'YOUR_PROJECT_ID_HERE', // من Firebase Console
    authDomain: 'YOUR_PROJECT_ID_HERE.firebaseapp.com',
    storageBucket: 'YOUR_PROJECT_ID_HERE.appspot.com',
    measurementId: 'YOUR_MEASUREMENT_ID_HERE', // من Firebase Console (اختياري)
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDemo-android-api-key-for-development',
    appId: '1:123456789:android:demo-app-id',
    messagingSenderId: '123456789',
    projectId: 'sales-tracker-demo',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDemo-ios-api-key-for-development',
    appId: '1:123456789:ios:demo-app-id',
    messagingSenderId: '123456789',
    projectId: 'sales-tracker-demo',
    iosBundleId: 'com.example.salesTracker',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDemo-macos-api-key-for-development',
    appId: '1:123456789:macos:demo-app-id',
    messagingSenderId: '123456789',
    projectId: 'sales-tracker-demo',
    iosBundleId: 'com.example.salesTracker',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDemo-windows-api-key-for-development',
    appId: '1:123456789:windows:demo-app-id',
    messagingSenderId: '123456789',
    projectId: 'sales-tracker-demo',
  );
}
