import 'package:flutter/material.dart';
import 'colors.dart';

class AppTextStyles {
  // Font Families
  static const String primaryFontFamily = 'Roboto';
  static const String arabicFontFamily = 'Cairo';
  
  // Font Weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  
  // Display Styles
  static const TextStyle displayLarge = TextStyle(
    fontSize: 57,
    fontWeight: regular,
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  static const TextStyle displayMedium = TextStyle(
    fontSize: 45,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.16,
  );
  
  static const TextStyle displaySmall = TextStyle(
    fontSize: 36,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.22,
  );
  
  // Headline Styles
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 32,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.25,
  );
  
  static const TextStyle headlineMedium = TextStyle(
    fontSize: 28,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.29,
  );
  
  static const TextStyle headlineSmall = TextStyle(
    fontSize: 24,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.33,
  );
  
  // Title Styles
  static const TextStyle titleLarge = TextStyle(
    fontSize: 22,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.27,
  );
  
  static const TextStyle titleMedium = TextStyle(
    fontSize: 16,
    fontWeight: medium,
    letterSpacing: 0.15,
    height: 1.50,
  );
  
  static const TextStyle titleSmall = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.1,
    height: 1.43,
  );
  
  // Label Styles
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.1,
    height: 1.43,
  );
  
  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
    height: 1.33,
  );
  
  static const TextStyle labelSmall = TextStyle(
    fontSize: 11,
    fontWeight: medium,
    letterSpacing: 0.5,
    height: 1.45,
  );
  
  // Body Styles
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
    height: 1.50,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0.25,
    height: 1.43,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
    height: 1.33,
  );
  
  // Custom App Styles
  static const TextStyle appBarTitle = TextStyle(
    fontSize: 20,
    fontWeight: medium,
    letterSpacing: 0.15,
  );
  
  static const TextStyle cardTitle = TextStyle(
    fontSize: 18,
    fontWeight: semiBold,
    letterSpacing: 0.15,
  );
  
  static const TextStyle cardSubtitle = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0.25,
  );
  
  static const TextStyle buttonText = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.1,
  );
  
  static const TextStyle inputLabel = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
  );
  
  static const TextStyle inputText = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
  );
  
  static const TextStyle inputHint = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
  );
  
  static const TextStyle errorText = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
  );
  
  static const TextStyle helperText = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
  );
  
  // Chart Styles
  static const TextStyle chartTitle = TextStyle(
    fontSize: 18,
    fontWeight: semiBold,
    letterSpacing: 0.15,
  );
  
  static const TextStyle chartLabel = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
  );
  
  static const TextStyle chartValue = TextStyle(
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.25,
  );
  
  // Statistics Styles
  static const TextStyle statisticValue = TextStyle(
    fontSize: 24,
    fontWeight: bold,
    letterSpacing: 0,
  );
  
  static const TextStyle statisticLabel = TextStyle(
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
  );
  
  // Navigation Styles
  static const TextStyle navigationLabel = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
  );
  
  static const TextStyle drawerItem = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
  );
  
  // Dialog Styles
  static const TextStyle dialogTitle = TextStyle(
    fontSize: 20,
    fontWeight: medium,
    letterSpacing: 0.15,
  );
  
  static const TextStyle dialogContent = TextStyle(
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.15,
  );
  
  // Snackbar Styles
  static const TextStyle snackbarText = TextStyle(
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0.25,
  );
  
  // Helper Methods
  static TextStyle getTextStyle({
    required double fontSize,
    FontWeight fontWeight = regular,
    Color? color,
    double? letterSpacing,
    double? height,
    String? fontFamily,
  }) {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
      fontFamily: fontFamily,
    );
  }
  
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
  
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
  
  // Theme-specific styles
  static TextStyle getLightTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.textPrimaryLight);
  }
  
  static TextStyle getDarkTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.textPrimaryDark);
  }
  
  static TextStyle getSecondaryLightTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.textSecondaryLight);
  }
  
  static TextStyle getSecondaryDarkTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.textSecondaryDark);
  }
  
  // Arabic text styles
  static TextStyle getArabicTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontFamily: arabicFontFamily,
      height: 1.6, // Better line height for Arabic text
    );
  }
  
  // Currency styles
  static const TextStyle currencyValue = TextStyle(
    fontSize: 20,
    fontWeight: bold,
    letterSpacing: 0,
  );
  
  static const TextStyle currencySymbol = TextStyle(
    fontSize: 16,
    fontWeight: medium,
    letterSpacing: 0.15,
  );
  
  // Status styles
  static const TextStyle statusActive = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
    color: AppColors.success,
  );
  
  static const TextStyle statusInactive = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
    color: AppColors.error,
  );
  
  static const TextStyle statusPending = TextStyle(
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
    color: AppColors.warning,
  );
}
