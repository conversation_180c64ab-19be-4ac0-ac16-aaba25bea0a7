import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';

/// وظائف مساعدة لإعداد حساب المدير
class AdminSetup {
  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'M1158m';
  static const String adminName = 'محمد جاسم';
  static const String adminDepartment = 'الإدارة';

  /// إنشاء حساب المدير الرئيسي
  static Future<bool> createAdminAccount() async {
    try {
      // التحقق من وجود الحساب مسبقاً
      final existingUser = await _checkIfAdminExists();
      if (existingUser != null) {
        print('✅ حساب المدير موجود بالفعل: ${existingUser.email}');
        return true;
      }

      // إنشاء حساب في Firebase Authentication
      final userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(
            email: adminEmail,
            password: adminPassword,
          );

      if (userCredential.user != null) {
        // إنشاء بيانات المستخدم في Firestore
        await _createAdminUserDocument(userCredential.user!);

        print('✅ تم إنشاء حساب المدير بنجاح');
        print('📧 البريد الإلكتروني: $adminEmail');
        print('🔑 كلمة المرور: $adminPassword');

        return true;
      }

      return false;
    } catch (e) {
      print('❌ خطأ في إنشاء حساب المدير: $e');
      return false;
    }
  }

  /// التحقق من وجود حساب المدير
  static Future<User?> _checkIfAdminExists() async {
    try {
      // محاولة تسجيل الدخول للتحقق من وجود الحساب
      final userCredential = await FirebaseAuth.instance
          .signInWithEmailAndPassword(
            email: adminEmail,
            password: adminPassword,
          );

      // تسجيل الخروج فوراً
      await FirebaseAuth.instance.signOut();

      return userCredential.user;
    } catch (e) {
      // الحساب غير موجود
      return null;
    }
  }

  /// إنشاء مستند المستخدم في Firestore
  static Future<void> _createAdminUserDocument(User user) async {
    final userModel = UserModel(
      id: user.uid,
      email: user.email!,
      name: adminName,
      role: 'admin',
      department: adminDepartment,
      phone: null,
      profileImageUrl: null,
      isActive: true,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );

    // إضافة البيانات الإضافية للمدير
    final adminData = userModel.toMap();
    adminData.addAll({
      'permissions': [
        'read_all',
        'write_all',
        'manage_users',
        'view_reports',
        'manage_products',
        'export_data',
        'system_settings',
      ],
      'updatedAt': FieldValue.serverTimestamp(),
    });

    await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .set(adminData);
  }

  /// تحديث صلاحيات المدير
  static Future<bool> updateAdminPermissions(String userId) async {
    try {
      await FirebaseFirestore.instance.collection('users').doc(userId).update({
        'role': 'admin',
        'isAdmin': true,
        'isEmployee': false,
        'permissions': [
          'read_all',
          'write_all',
          'manage_users',
          'view_reports',
          'manage_products',
          'export_data',
          'system_settings',
        ],
        'updatedAt': FieldValue.serverTimestamp(),
      });

      print('✅ تم تحديث صلاحيات المدير بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في تحديث صلاحيات المدير: $e');
      return false;
    }
  }

  /// البحث عن مستخدم بالبريد الإلكتروني وتحويله إلى مدير
  static Future<bool> promoteUserToAdmin(String email) async {
    try {
      final querySnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .where('email', isEqualTo: email)
              .get();

      if (querySnapshot.docs.isNotEmpty) {
        final userId = querySnapshot.docs.first.id;
        return await updateAdminPermissions(userId);
      } else {
        print('❌ لم يتم العثور على مستخدم بهذا البريد الإلكتروني: $email');
        return false;
      }
    } catch (e) {
      print('❌ خطأ في البحث عن المستخدم: $e');
      return false;
    }
  }

  /// التحقق من صلاحيات المدير
  static Future<bool> verifyAdminAccess(String userId) async {
    try {
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final isAdmin = userData['isAdmin'] as bool? ?? false;
        final role = userData['role'] as String? ?? '';

        return isAdmin && role == 'admin';
      }

      return false;
    } catch (e) {
      print('❌ خطأ في التحقق من صلاحيات المدير: $e');
      return false;
    }
  }

  /// طباعة معلومات حساب المدير
  static void printAdminInfo() {
    print('🔐 معلومات حساب المدير:');
    print('📧 البريد الإلكتروني: $adminEmail');
    print('🔑 كلمة المرور: $adminPassword');
    print('👤 الاسم: $adminName');
    print('🏢 القسم: $adminDepartment');
    print('🎯 الدور: مدير (Admin)');
    print('✅ الصلاحيات: كاملة');
  }
}
